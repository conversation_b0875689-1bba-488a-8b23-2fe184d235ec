'use client'

import React, { useRef, useState } from 'react'
import CrewTrainingList from './list'

import { CrewTrainingFilterActions } from '@/components/filter/components/training-actions'
import { SealogsTrainingIcon } from '@/app/lib/icons/SealogsTrainingIcon'
import { ListHeader } from '@/components/ui/list-header'
import { useQueryState, parseAsBoolean } from 'nuqs'

type FilterHandle = {
    apply: (p: { type: string; data: any }) => void
    overdue: boolean // read-only snapshot
    setOverdue: (v: boolean) => void
}

const CrewTrainingClient = () => {
    const applyFilterRef = useRef<FilterHandle>(null)

    /** ⬅️ 1) reactive state that drives the heading */
    const [isOverdueEnabled, setIsOverdueEnabled] = useQueryState(
        'overdue',
        parseAsBoolean.withDefault(false),
    )

    /** ⬅️ 2) keep both the list and the heading in sync */
    const handleDropdownChange = (type: string, data: any) => {
        if (type === 'overdue') setIsOverdueEnabled(!!data)
        applyFilterRef.current?.apply({ type, data })
    }

    return (
        <div className="block w-full">
            <ListHeader
                icon={
                    <SealogsTrainingIcon
                        className={`h-12 w-12 ring-1 p-0.5 rounded-full bg-[#fff]`}
                    />
                }
                title={`${
                    !isOverdueEnabled ? 'Overdue and upcoming' : 'Completed'
                } trainings`}
                actions={
                    <CrewTrainingFilterActions
                        onChange={(data: any) => {
                            handleDropdownChange('overdue', data)
                        }}
                        overdueList={isOverdueEnabled}
                    />
                }
            />
            <div className="mt-16">
                <CrewTrainingList
                    applyFilterRef={applyFilterRef}
                    excludeFilters={['overdueToggle']}
                />
            </div>
        </div>
    )
}

export default CrewTrainingClient
