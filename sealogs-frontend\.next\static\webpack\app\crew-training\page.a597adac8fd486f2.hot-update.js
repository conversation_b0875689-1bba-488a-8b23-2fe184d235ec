"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // const [filter, setFilter] = useState({})\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isVesselView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overdueSwitcher, setOverdueSwitcher] = (0,nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState)(\"overdue\");\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    // Create a boolean state wrapper for the useTrainingFilters hook\n    const [overdueBoolean, setOverdueBoolean] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Sync the boolean state with the query state\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setOverdueBoolean(overdueSwitcher === \"true\");\n    }, [\n        overdueSwitcher\n    ]);\n    // Create a wrapper function that converts boolean to string for the query state\n    const toggleOverdueWrapper = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        if (typeof value === \"function\") {\n            setOverdueBoolean((prev)=>{\n                const newValue = value(prev);\n                setOverdueSwitcher(newValue ? \"true\" : \"false\");\n                return newValue;\n            });\n        } else {\n            setOverdueBoolean(value);\n            setOverdueSwitcher(value ? \"true\" : \"false\");\n        }\n    }, [\n        setOverdueSwitcher\n    ]);\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            if (transformedData) {\n                setTrainingList(transformedData);\n                setVesselIdOptions(vesselIDs);\n                setTrainingTypeIdOptions(trainingTypeIDs);\n                setTrainerIdOptions(trainerIDs);\n                setCrewIdOptions(memberIDs);\n            }\n            setPageInfo(response.readTrainingSessions.pageInfo);\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadTrainingSessionDues(filter);\n        loadTrainingList(newPage, filter);\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: toggleOverdueWrapper\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: overdueBoolean,\n            setOverdue: toggleOverdueWrapper\n        };\n    }, [\n        handleFilterChange,\n        overdueBoolean,\n        toggleOverdueWrapper\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match unified structure\n    const transformCompletedTrainings = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                trainer: training.trainer,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                trainingLocationType: training.trainingLocationType,\n                // Add priority for sorting (higher number = higher priority)\n                priority: 0\n            };\n        });\n    };\n    // Create unified and sorted training data\n    const createUnifiedTrainingData = ()=>{\n        // Transform completed trainings\n        const transformedCompleted = transformCompletedTrainings(trainingList || []);\n        // Add priority to overdue/upcoming trainings\n        const prioritizedDues = (trainingSessionDues || []).map((due)=>{\n            var _due_status, _due_status1;\n            return {\n                ...due,\n                priority: ((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? 3 : ((_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.dueWithinSevenDays) ? 2 : 1\n            };\n        });\n        // Combine all training data\n        const allTrainings = [\n            ...prioritizedDues,\n            ...transformedCompleted\n        ];\n        // Sort by priority (descending), then by due date (ascending for overdue/upcoming, descending for completed)\n        return allTrainings.sort((a, b)=>{\n            // First sort by priority (overdue > upcoming > other > completed)\n            if (a.priority !== b.priority) {\n                return b.priority - a.priority;\n            }\n            // Within same priority, sort by date\n            const dateA = new Date(a.dueDate || 0).getTime();\n            const dateB = new Date(b.dueDate || 0).getTime();\n            // For overdue and upcoming (priority > 0), sort by due date ascending (earliest first)\n            if (a.priority > 0) {\n                return dateA - dateB;\n            }\n            // For completed trainings (priority = 0), sort by date descending (most recent first)\n            return dateB - dateA;\n        });\n    };\n    // Combined loading state for unified view\n    const isUnifiedDataLoading = excludeFilters.includes(\"overdueToggle\") ? trainingListLoading || trainingSessionDuesLoading : false;\n    // Row status evaluation for FilteredTable styling\n    const getTrainingRowStatus = (training)=>{\n        if (!training.status) return \"normal\";\n        if (training.status.isOverdue) {\n            return \"overdue\";\n        }\n        if (training.status.dueWithinSevenDays) {\n            return \"upcoming\";\n        }\n        return \"normal\";\n    };\n    // Unified column definitions for the training table\n    const createUnifiedTrainingColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: \"Training\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                        data: training,\n                        memberId: memberId,\n                        type: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.label) === \"Completed\" ? \"completed\" : \"overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_trainingType, _rowB_original1;\n                    // Sort by priority first, then by training type name\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    if (priorityA !== priorityB) {\n                        return priorityB - priorityA;\n                    }\n                    const nameA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const nameB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return nameA.localeCompare(nameB);\n                }\n            },\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_trainingType, _training_trainingTypes_nodes, _training_trainingTypes, _training_status, _training_status1;\n                    const training = row.original;\n                    const trainingType = ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : _training_trainingTypes_nodes.map((t)=>t.title).join(\", \")) || \"\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-sm font-medium\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: trainingType\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_trainingType, _rowA_original, _rowB_original_trainingType, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_14__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Crew\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    const members = training.members || [];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 justify-center\",\n                        children: [\n                            members.slice(0, 5).map((member)=>/*#__PURE__*/ {\n                                var _training_status, _training_status1;\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                                size: \"sm\",\n                                                variant: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? \"destructive\" : ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) ? \"warning\" : \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 33\n                                }, undefined);\n                            }),\n                            members.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground ml-1\",\n                                children: [\n                                    \"+\",\n                                    members.length - 5\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_members_, _rowA_original_members, _rowA_original, _rowA_original_members_1, _rowA_original_members1, _rowA_original1, _rowB_original_members_, _rowB_original_members, _rowB_original, _rowB_original_members_1, _rowB_original_members1, _rowB_original1;\n                    var _rowA_original_members__firstName, _rowA_original_members__surname;\n                    const valueA = \"\".concat((_rowA_original_members__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_ = _rowA_original_members[0]) === null || _rowA_original_members_ === void 0 ? void 0 : _rowA_original_members_.firstName) !== null && _rowA_original_members__firstName !== void 0 ? _rowA_original_members__firstName : \"\", \" \").concat((_rowA_original_members__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_1 = _rowA_original_members1[0]) === null || _rowA_original_members_1 === void 0 ? void 0 : _rowA_original_members_1.surname) !== null && _rowA_original_members__surname !== void 0 ? _rowA_original_members__surname : \"\") || \"\";\n                    var _rowB_original_members__firstName, _rowB_original_members__surname;\n                    const valueB = \"\".concat((_rowB_original_members__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_ = _rowB_original_members[0]) === null || _rowB_original_members_ === void 0 ? void 0 : _rowB_original_members_.firstName) !== null && _rowB_original_members__firstName !== void 0 ? _rowB_original_members__firstName : \"\", \" \").concat((_rowB_original_members__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_1 = _rowB_original_members1[0]) === null || _rowB_original_members_1 === void 0 ? void 0 : _rowB_original_members_1.surname) !== null && _rowB_original_members__surname !== void 0 ? _rowB_original_members__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 605,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1, _training_status2, _training_status3;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded-md text-xs font-medium text-center\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"bg-destructive/10 text-destructive\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"bg-warning/10 text-warning\", ((_training_status2 = training.status) === null || _training_status2 === void 0 ? void 0 : _training_status2.label) === \"Completed\" && \"bg-muted text-muted-foreground\"),\n                        children: ((_training_status3 = training.status) === null || _training_status3 === void 0 ? void 0 : _training_status3.label) || \"Unknown\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    return priorityB - priorityA;\n                }\n            },\n            {\n                accessorKey: \"dueDate\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1;\n                    const training = row.original;\n                    const dateText = formatDate(training.dueDate);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-sm font-medium text-right\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: dateText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 643,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || 0).getTime();\n                    const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || 0).getTime();\n                    return dateB - dateA;\n                }\n            }\n        ]);\n    // Get unified training data based on filter settings\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            // Use the new unified and sorted data when overdueToggle is excluded\n            return createUnifiedTrainingData();\n        }\n        // Return only overdue/upcoming trainings when toggle is available\n        return trainingSessionDues || [];\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 685,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 687,\n            columnNumber: 13\n        }, undefined);\n    }\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Completed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 695,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: training,\n                    memberId: memberId,\n                    type: \"completed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 700,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.date) || 0).getTime();\n                const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.date) || 0).getTime();\n                return dateB - dateA;\n            }\n        },\n        {\n            accessorKey: \"trainingDrillsCompleted\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training/drills completed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 718,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.P, {\n                    children: training.trainingTypes.nodes ? training.trainingTypes.nodes.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                item.title,\n                                \",\\xa0\"\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 35\n                        }, undefined)) : \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 727,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_trainingTypes_nodes_, _rowA_original_trainingTypes_nodes, _rowA_original_trainingTypes, _rowA_original, _rowB_original_trainingTypes_nodes_, _rowB_original_trainingTypes_nodes, _rowB_original_trainingTypes, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingTypes = _rowA_original.trainingTypes) === null || _rowA_original_trainingTypes === void 0 ? void 0 : (_rowA_original_trainingTypes_nodes = _rowA_original_trainingTypes.nodes) === null || _rowA_original_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_trainingTypes_nodes_ = _rowA_original_trainingTypes_nodes[0]) === null || _rowA_original_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_trainingTypes_nodes_.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingTypes = _rowB_original.trainingTypes) === null || _rowB_original_trainingTypes === void 0 ? void 0 : (_rowB_original_trainingTypes_nodes = _rowB_original_trainingTypes.nodes) === null || _rowB_original_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_trainingTypes_nodes_ = _rowB_original_trainingTypes_nodes[0]) === null || _rowB_original_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_trainingTypes_nodes_.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"where\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Where\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 748,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _training_vessel;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-nowrap\",\n                            children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || training.trainingLocationType || \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 756,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_14__.LocationModal, {\n                            vessel: training.vessel,\n                            iconClassName: \"size-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 755,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"trainer\",\n            cellAlignment: \"center\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Trainer\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 779,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                var _training_trainer_surname, _training_trainer_surname1;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-nowrap\",\n                    children: !isVesselView ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(training.trainer.firstName, training.trainer.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 790,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 788,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                children: [\n                                    training.trainer.firstName,\n                                    \" \",\n                                    (_training_trainer_surname = training.trainer.surname) !== null && _training_trainer_surname !== void 0 ? _training_trainer_surname : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 798,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(training.trainer.firstName, training.trainer.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 807,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 806,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 805,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                children: [\n                                    training.trainer.firstName,\n                                    \" \",\n                                    (_training_trainer_surname1 = training.trainer.surname) !== null && _training_trainer_surname1 !== void 0 ? _training_trainer_surname1 : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 815,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 804,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 785,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original_trainer, _rowA_original1, _rowA_original2, _rowA_original_trainer1, _rowA_original3, _rowB_original, _rowB_original_trainer, _rowB_original1, _rowB_original2, _rowB_original_trainer1, _rowB_original3;\n                const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.trainer) && (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainer = _rowA_original1.trainer) === null || _rowA_original_trainer === void 0 ? void 0 : _rowA_original_trainer.firstName), \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original2 = rowA.original) === null || _rowA_original2 === void 0 ? void 0 : _rowA_original2.trainer) && (rowA === null || rowA === void 0 ? void 0 : (_rowA_original3 = rowA.original) === null || _rowA_original3 === void 0 ? void 0 : (_rowA_original_trainer1 = _rowA_original3.trainer) === null || _rowA_original_trainer1 === void 0 ? void 0 : _rowA_original_trainer1.surname)) || \"\";\n                const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.trainer) && (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainer = _rowB_original1.trainer) === null || _rowB_original_trainer === void 0 ? void 0 : _rowB_original_trainer.firstName), \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original2 = rowB.original) === null || _rowB_original2 === void 0 ? void 0 : _rowB_original2.trainer) && (rowB === null || rowB === void 0 ? void 0 : (_rowB_original3 = rowB.original) === null || _rowB_original3 === void 0 ? void 0 : (_rowB_original_trainer1 = _rowB_original3.trainer) === null || _rowB_original_trainer1 === void 0 ? void 0 : _rowB_original_trainer1.surname)) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"who\",\n            cellAlignment: \"right\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Who\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 838,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full flex items-end gap-1\",\n                    children: training.members.nodes.map((member, index)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 853,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 45\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 849,\n                                    columnNumber: 41\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 41\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 848,\n                            columnNumber: 37\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 844,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    // Get the unified training data\n    const unifiedTrainingData = getUnifiedTrainingData();\n    const isDataLoading = trainingListLoading || trainingSessionDuesLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: !overdueBoolean,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 892,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 891,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 890,\n                columnNumber: 13\n            }, undefined),\n            isDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 903,\n                        columnNumber: 21\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 902,\n                columnNumber: 17\n            }, undefined) : (unifiedTrainingData === null || unifiedTrainingData === void 0 ? void 0 : unifiedTrainingData.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.FilteredTable, {\n                columns: createUnifiedTrainingColumns(),\n                data: unifiedTrainingData,\n                rowStatus: getTrainingRowStatus,\n                pageSize: 20,\n                showToolbar: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 907,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (trainingList === null || trainingList === void 0 ? void 0 : trainingList.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n                    columns: columns,\n                    data: trainingList,\n                    pageSize: 20,\n                    onChange: handleFilterChange,\n                    showToolbar: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 917,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"group border-b hover: \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 col-span-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"!w-[75px] h-auto\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    viewBox: \"0 0 147 147.01\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 932,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                            fill: \"#052350\",\n                                            fillRule: \"evenodd\",\n                                            opacity: \".97\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 949,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                            fill: \"#024450\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 954,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                            fill: \"#052451\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 974,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 979,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 994,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 928,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"  \",\n                                    children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1010,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 926,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 925,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false),\n            !excludeFilters.includes(\"overdueToggle\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full\",\n                onClick: ()=>toggleOverdueWrapper((prev)=>!prev),\n                children: overdueBoolean ? \"View all completed trainings\" : \"View overdue trainings\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 1022,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 889,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"MHQV5rmcFGZBtb2m8YD7JtdoAFo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, isVesselView = false, hideCrewColumn = false, pageSize = 20 } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery)(\"(min-width: 720px)\");\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Training\",\n            cell: (param)=>{\n                let { row } = param;\n                const data = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: data,\n                    type: \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 1050,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            cellAlignment: \"left\",\n            header: \"Vessel\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_vessel;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isVesselView == false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                        children: ((_due_vessel = due.vessel) === null || _due_vessel === void 0 ? void 0 : _due_vessel.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 1063,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"crew\",\n            cellAlignment: \"right\",\n            header: \"Crew\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status;\n                const due = row.original;\n                const members = due.members || [];\n                return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: members.map((member)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: due.status.isOverdue ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 1098,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 1091,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1106,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 1089,\n                            columnNumber: 33\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 1086,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"!rounded-full size-10\", (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.class),\n                    children: members.length\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 1115,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            cellAlignment: \"right\",\n            header: \"Status\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status, _due_status1, _due_status2;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? (_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                    children: ((_due_status2 = due.status) === null || _due_status2 === void 0 ? void 0 : _due_status2.label) || \"Unknown Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 1142,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"crew\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n            columns: columns,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 1159,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"  \",\n                        children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 1169,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 1168,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 1167,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 1166,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s1(OverdueTrainingList, \"mDuYwCIVI9QBRqOzz3Dco716Kgk=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery\n    ];\n});\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});