"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData)();\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D Raw training sessions data from API:\", response.readTrainingSessions);\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            console.log(\"\\uD83D\\uDD04 Transformed training sessions data:\", transformedData);\n            if (transformedData) {\n                setTrainingList(transformedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: ()=>{}\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: false,\n            setOverdue: ()=>{}\n        };\n    }, [\n        handleFilterChange\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D Raw training session dues data from API:\", response.readTrainingSessionDues);\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                console.log(\"\\uD83D\\uDD04 Processed training session dues:\", mergedDues);\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match unified structure\n    const transformCompletedTrainings = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                trainer: training.trainer,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                trainingLocationType: training.trainingLocationType,\n                // Add priority for sorting (higher number = higher priority)\n                priority: 0\n            };\n        });\n    };\n    // Create unified and sorted training data\n    const createUnifiedTrainingData = ()=>{\n        // Transform completed trainings\n        const transformedCompleted = transformCompletedTrainings(trainingList || []);\n        // Add priority to overdue/upcoming trainings\n        const prioritizedDues = (trainingSessionDues || []).map((due)=>{\n            var _due_status, _due_status1;\n            return {\n                ...due,\n                priority: ((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? 3 : ((_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.dueWithinSevenDays) ? 2 : 1\n            };\n        });\n        // Combine all training data\n        const allTrainings = [\n            ...prioritizedDues,\n            ...transformedCompleted\n        ];\n        // Sort by priority (descending), then by due date (ascending for overdue/upcoming, descending for completed)\n        return allTrainings.sort((a, b)=>{\n            // First sort by priority (overdue > upcoming > other > completed)\n            if (a.priority !== b.priority) {\n                return b.priority - a.priority;\n            }\n            // Within same priority, sort by date\n            const dateA = new Date(a.dueDate || 0).getTime();\n            const dateB = new Date(b.dueDate || 0).getTime();\n            // For overdue and upcoming (priority > 0), sort by due date ascending (earliest first)\n            if (a.priority > 0) {\n                return dateA - dateB;\n            }\n            // For completed trainings (priority = 0), sort by date descending (most recent first)\n            return dateB - dateA;\n        });\n    };\n    // Combined loading state for unified view\n    const isUnifiedDataLoading = excludeFilters.includes(\"overdueToggle\") ? trainingListLoading || trainingSessionDuesLoading : false;\n    // Row status evaluation for FilteredTable styling\n    const getTrainingRowStatus = (training)=>{\n        if (!training.status) return \"normal\";\n        if (training.status.isOverdue) {\n            return \"overdue\";\n        }\n        if (training.status.dueWithinSevenDays) {\n            return \"upcoming\";\n        }\n        return \"normal\";\n    };\n    // Unified column definitions for the training table\n    const createUnifiedTrainingColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: \"Training\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__.MobileTrainingCard, {\n                        data: training,\n                        memberId: memberId,\n                        type: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.label) === \"Completed\" ? \"completed\" : \"overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_trainingType, _rowB_original1;\n                    // Sort by priority first, then by training type name\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    if (priorityA !== priorityB) {\n                        return priorityB - priorityA;\n                    }\n                    const nameA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const nameB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return nameA.localeCompare(nameB);\n                }\n            },\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_trainingType, _training_trainingTypes_nodes, _training_trainingTypes, _training_status, _training_status1;\n                    const training = row.original;\n                    const trainingType = ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : _training_trainingTypes_nodes.map((t)=>t.title).join(\", \")) || \"\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-sm font-medium\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: trainingType\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_trainingType, _rowA_original, _rowB_original_trainingType, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_13__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Crew\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    const members = training.members || [];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 justify-center\",\n                        children: [\n                            members.slice(0, 5).map((member)=>/*#__PURE__*/ {\n                                var _training_status, _training_status1;\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                                size: \"sm\",\n                                                variant: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? \"destructive\" : ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) ? \"warning\" : \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 33\n                                }, undefined);\n                            }),\n                            members.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground ml-1\",\n                                children: [\n                                    \"+\",\n                                    members.length - 5\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_members_, _rowA_original_members, _rowA_original, _rowA_original_members_1, _rowA_original_members1, _rowA_original1, _rowB_original_members_, _rowB_original_members, _rowB_original, _rowB_original_members_1, _rowB_original_members1, _rowB_original1;\n                    var _rowA_original_members__firstName, _rowA_original_members__surname;\n                    const valueA = \"\".concat((_rowA_original_members__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_ = _rowA_original_members[0]) === null || _rowA_original_members_ === void 0 ? void 0 : _rowA_original_members_.firstName) !== null && _rowA_original_members__firstName !== void 0 ? _rowA_original_members__firstName : \"\", \" \").concat((_rowA_original_members__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_1 = _rowA_original_members1[0]) === null || _rowA_original_members_1 === void 0 ? void 0 : _rowA_original_members_1.surname) !== null && _rowA_original_members__surname !== void 0 ? _rowA_original_members__surname : \"\") || \"\";\n                    var _rowB_original_members__firstName, _rowB_original_members__surname;\n                    const valueB = \"\".concat((_rowB_original_members__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_ = _rowB_original_members[0]) === null || _rowB_original_members_ === void 0 ? void 0 : _rowB_original_members_.firstName) !== null && _rowB_original_members__firstName !== void 0 ? _rowB_original_members__firstName : \"\", \" \").concat((_rowB_original_members__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_1 = _rowB_original_members1[0]) === null || _rowB_original_members_1 === void 0 ? void 0 : _rowB_original_members_1.surname) !== null && _rowB_original_members__surname !== void 0 ? _rowB_original_members__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1, _training_status2, _training_status3;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"px-2 py-1 rounded-md text-xs font-medium text-center\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"bg-destructive/10 text-destructive\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"bg-warning/10 text-warning\", ((_training_status2 = training.status) === null || _training_status2 === void 0 ? void 0 : _training_status2.label) === \"Completed\" && \"bg-muted text-muted-foreground\"),\n                        children: ((_training_status3 = training.status) === null || _training_status3 === void 0 ? void 0 : _training_status3.label) || \"Unknown\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    return priorityB - priorityA;\n                }\n            },\n            {\n                accessorKey: \"dueDate\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1;\n                    const training = row.original;\n                    const dateText = formatDate(training.dueDate);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-sm font-medium text-right\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: dateText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 584,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || 0).getTime();\n                    const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || 0).getTime();\n                    return dateB - dateA;\n                }\n            }\n        ]);\n    // Get unified training data based on filter settings\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            // Use the new unified and sorted data when overdueToggle is excluded\n            const unifiedData = createUnifiedTrainingData();\n            console.log(\"\\uD83D\\uDCCA Final unified training data for table:\", unifiedData);\n            return unifiedData;\n        }\n        // Return only overdue/upcoming trainings when toggle is available\n        const duesData = trainingSessionDues || [];\n        console.log(\"\\uD83D\\uDCCA Final training dues data for table:\", duesData);\n        return duesData;\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 633,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 635,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Get the unified training data\n    const unifiedTrainingData = getUnifiedTrainingData();\n    const isDataLoading = trainingListLoading || trainingSessionDuesLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: false,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 646,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 645,\n                columnNumber: 13\n            }, undefined),\n            isDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 21\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 657,\n                columnNumber: 17\n            }, undefined) : (unifiedTrainingData === null || unifiedTrainingData === void 0 ? void 0 : unifiedTrainingData.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.FilteredTable, {\n                columns: createUnifiedTrainingColumns(),\n                data: unifiedTrainingData,\n                rowStatus: getTrainingRowStatus,\n                pageSize: 20,\n                showToolbar: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 662,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 col-span-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 673,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 671,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 670,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 644,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"qGRjJhH5zy1byOOMmIbrnA6YUWY=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, isVesselView = false, hideCrewColumn = false, pageSize = 20 } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery)(\"(min-width: 720px)\");\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Training\",\n            cell: (param)=>{\n                let { row } = param;\n                const data = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__.MobileTrainingCard, {\n                    data: data,\n                    type: \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 701,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            cellAlignment: \"left\",\n            header: \"Vessel\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_vessel;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isVesselView == false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                        children: ((_due_vessel = due.vessel) === null || _due_vessel === void 0 ? void 0 : _due_vessel.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"crew\",\n            cellAlignment: \"right\",\n            header: \"Crew\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status;\n                const due = row.original;\n                const members = due.members || [];\n                return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: members.map((member)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                        size: \"sm\",\n                                        variant: due.status.isOverdue ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 33\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 737,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"!rounded-full size-10\", (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.class),\n                    children: members.length\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 766,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            cellAlignment: \"right\",\n            header: \"Status\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status, _due_status1, _due_status2;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? (_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                    children: ((_due_status2 = due.status) === null || _due_status2 === void 0 ? void 0 : _due_status2.label) || \"Unknown Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 793,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"crew\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n            columns: columns,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 810,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"  \",\n                        children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 820,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 819,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 818,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 817,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s1(OverdueTrainingList, \"mDuYwCIVI9QBRqOzz3Dco716Kgk=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery\n    ];\n});\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});