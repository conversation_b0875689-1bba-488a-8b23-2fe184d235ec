"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData)();\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D Raw training sessions data from API:\", response.readTrainingSessions);\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            console.log(\"\\uD83D\\uDD04 Transformed training sessions data:\", transformedData);\n            if (transformedData) {\n                setTrainingList(transformedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: ()=>{}\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: false,\n            setOverdue: ()=>{}\n        };\n    }, [\n        handleFilterChange\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D Raw training session dues data from API:\", response.readTrainingSessionDues);\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                console.log(\"\\uD83D\\uDD04 Processed training session dues:\", mergedDues);\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match unified structure\n    const transformCompletedTrainings = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                trainer: training.trainer,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                trainingLocationType: training.trainingLocationType,\n                // Add priority for sorting (higher number = higher priority)\n                priority: 0\n            };\n        });\n    };\n    // Create unified and sorted training data\n    const createUnifiedTrainingData = ()=>{\n        // Transform completed trainings\n        const transformedCompleted = transformCompletedTrainings(trainingList || []);\n        // Add priority to overdue/upcoming trainings\n        const prioritizedDues = (trainingSessionDues || []).map((due)=>{\n            var _due_status, _due_status1;\n            return {\n                ...due,\n                priority: ((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? 3 : ((_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.dueWithinSevenDays) ? 2 : 1\n            };\n        });\n        // Combine all training data\n        const allTrainings = [\n            ...prioritizedDues,\n            ...transformedCompleted\n        ];\n        // Sort by priority (descending), then by due date (ascending for overdue/upcoming, descending for completed)\n        return allTrainings.sort((a, b)=>{\n            // First sort by priority (overdue > upcoming > other > completed)\n            if (a.priority !== b.priority) {\n                return b.priority - a.priority;\n            }\n            // Within same priority, sort by date\n            const dateA = new Date(a.dueDate || 0).getTime();\n            const dateB = new Date(b.dueDate || 0).getTime();\n            // For overdue and upcoming (priority > 0), sort by due date ascending (earliest first)\n            if (a.priority > 0) {\n                return dateA - dateB;\n            }\n            // For completed trainings (priority = 0), sort by date descending (most recent first)\n            return dateB - dateA;\n        });\n    };\n    // Row status evaluation for FilteredTable styling\n    const getTrainingRowStatus = (training)=>{\n        if (!training.status) return \"normal\";\n        if (training.status.isOverdue) {\n            return \"overdue\";\n        }\n        if (training.status.dueWithinSevenDays) {\n            return \"upcoming\";\n        }\n        return \"normal\";\n    };\n    // Unified column definitions for the training table\n    const createUnifiedTrainingColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: \"Training\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__.MobileTrainingCard, {\n                        data: training,\n                        memberId: memberId,\n                        type: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.label) === \"Completed\" ? \"completed\" : \"overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_trainingType, _rowB_original1;\n                    // Sort by priority first, then by training type name\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    if (priorityA !== priorityB) {\n                        return priorityB - priorityA;\n                    }\n                    const nameA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const nameB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return nameA.localeCompare(nameB);\n                }\n            },\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_trainingType, _training_trainingTypes_nodes, _training_trainingTypes, _training_status, _training_status1;\n                    const training = row.original;\n                    const trainingType = ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : _training_trainingTypes_nodes.map((t)=>t.title).join(\", \")) || \"\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-sm font-medium\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: trainingType\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_trainingType, _rowA_original, _rowB_original_trainingType, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_13__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Crew\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    const members = training.members || [];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 justify-center\",\n                        children: [\n                            members.slice(0, 5).map((member)=>/*#__PURE__*/ {\n                                var _training_status, _training_status1;\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                                size: \"sm\",\n                                                variant: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? \"destructive\" : ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) ? \"warning\" : \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 33\n                                }, undefined);\n                            }),\n                            members.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground ml-1\",\n                                children: [\n                                    \"+\",\n                                    members.length - 5\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_members_, _rowA_original_members, _rowA_original, _rowA_original_members_1, _rowA_original_members1, _rowA_original1, _rowB_original_members_, _rowB_original_members, _rowB_original, _rowB_original_members_1, _rowB_original_members1, _rowB_original1;\n                    var _rowA_original_members__firstName, _rowA_original_members__surname;\n                    const valueA = \"\".concat((_rowA_original_members__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_ = _rowA_original_members[0]) === null || _rowA_original_members_ === void 0 ? void 0 : _rowA_original_members_.firstName) !== null && _rowA_original_members__firstName !== void 0 ? _rowA_original_members__firstName : \"\", \" \").concat((_rowA_original_members__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_1 = _rowA_original_members1[0]) === null || _rowA_original_members_1 === void 0 ? void 0 : _rowA_original_members_1.surname) !== null && _rowA_original_members__surname !== void 0 ? _rowA_original_members__surname : \"\") || \"\";\n                    var _rowB_original_members__firstName, _rowB_original_members__surname;\n                    const valueB = \"\".concat((_rowB_original_members__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_ = _rowB_original_members[0]) === null || _rowB_original_members_ === void 0 ? void 0 : _rowB_original_members_.firstName) !== null && _rowB_original_members__firstName !== void 0 ? _rowB_original_members__firstName : \"\", \" \").concat((_rowB_original_members__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_1 = _rowB_original_members1[0]) === null || _rowB_original_members_1 === void 0 ? void 0 : _rowB_original_members_1.surname) !== null && _rowB_original_members__surname !== void 0 ? _rowB_original_members__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1, _training_status2, _training_status3;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"px-2 py-1 rounded-md text-xs font-medium text-center\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"bg-destructive/10 text-destructive\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"bg-warning/10 text-warning\", ((_training_status2 = training.status) === null || _training_status2 === void 0 ? void 0 : _training_status2.label) === \"Completed\" && \"bg-muted text-muted-foreground\"),\n                        children: ((_training_status3 = training.status) === null || _training_status3 === void 0 ? void 0 : _training_status3.label) || \"Unknown\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    return priorityB - priorityA;\n                }\n            },\n            {\n                accessorKey: \"dueDate\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1;\n                    const training = row.original;\n                    const dateText = formatDate(training.dueDate);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-sm font-medium text-right\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: dateText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || 0).getTime();\n                    const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || 0).getTime();\n                    return dateB - dateA;\n                }\n            }\n        ]);\n    // Get unified training data based on filter settings\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            // Use the new unified and sorted data when overdueToggle is excluded\n            const unifiedData = createUnifiedTrainingData();\n            console.log(\"\\uD83D\\uDCCA Final unified training data for table:\", unifiedData);\n            return unifiedData;\n        }\n        // Return only overdue/upcoming trainings when toggle is available\n        const duesData = trainingSessionDues || [];\n        console.log(\"\\uD83D\\uDCCA Final training dues data for table:\", duesData);\n        return duesData;\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 628,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 630,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Get the unified training data\n    const unifiedTrainingData = getUnifiedTrainingData();\n    const isDataLoading = trainingListLoading || trainingSessionDuesLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: false,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 641,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 640,\n                columnNumber: 13\n            }, undefined),\n            isDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 21\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 652,\n                columnNumber: 17\n            }, undefined) : (unifiedTrainingData === null || unifiedTrainingData === void 0 ? void 0 : unifiedTrainingData.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.FilteredTable, {\n                columns: createUnifiedTrainingColumns(),\n                data: unifiedTrainingData,\n                rowStatus: getTrainingRowStatus,\n                pageSize: 20,\n                showToolbar: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 657,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 col-span-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 667,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 666,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 665,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 639,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"qGRjJhH5zy1byOOMmIbrnA6YUWY=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, isVesselView = false, hideCrewColumn = false, pageSize = 20 } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery)(\"(min-width: 720px)\");\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Training\",\n            cell: (param)=>{\n                let { row } = param;\n                const data = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__.MobileTrainingCard, {\n                    data: data,\n                    type: \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            cellAlignment: \"left\",\n            header: \"Vessel\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_vessel;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isVesselView == false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                        children: ((_due_vessel = due.vessel) === null || _due_vessel === void 0 ? void 0 : _due_vessel.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 709,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"crew\",\n            cellAlignment: \"right\",\n            header: \"Crew\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status;\n                const due = row.original;\n                const members = due.members || [];\n                return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: members.map((member)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                        size: \"sm\",\n                                        variant: due.status.isOverdue ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 33\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 732,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"!rounded-full size-10\", (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.class),\n                    children: members.length\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 761,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            cellAlignment: \"right\",\n            header: \"Status\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status, _due_status1, _due_status2;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? (_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                    children: ((_due_status2 = due.status) === null || _due_status2 === void 0 ? void 0 : _due_status2.label) || \"Unknown Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 788,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"crew\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n            columns: columns,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 805,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"  \",\n                        children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 814,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 813,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 812,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s1(OverdueTrainingList, \"mDuYwCIVI9QBRqOzz3Dco716Kgk=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery\n    ];\n});\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy9saXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtnQztBQUNhO0FBQ1c7QUFFQTtBQUNJO0FBQzNCO0FBQ3VDO0FBRXhFLGlEQUFpRDtBQUNqRCxNQUFNVSxhQUFhLENBQUNDO0lBQ2hCLElBQUksQ0FBQ0EsWUFBWSxPQUFPO0lBQ3hCLElBQUk7UUFDQSxNQUFNQyxPQUFPLElBQUlDLEtBQUtGO1FBQ3RCLE9BQU9KLDhFQUFNQSxDQUFDSyxNQUFNO0lBQ3hCLEVBQUUsVUFBTTtRQUNKLE9BQU87SUFDWDtBQUNKO0FBQ21DO0FBT0E7QUFDc0M7QUFDbkM7QUFTZDtBQUN1QjtBQUNYO0FBQzJCO0FBQ2hCO0FBQ2lCO0FBRU07QUFFdEUsTUFBTXFCLG1CQUFtQjtRQUFDLEVBQ3RCQyxXQUFXLENBQUMsRUFDWkMsV0FBVyxDQUFDLEVBQ1pDLGNBQWMsRUFDZEMsaUJBQWlCLEVBQUUsRUFNdEI7O0lBQ0csTUFBTUMsUUFBUTtJQUNkLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHcEMsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDcUMsY0FBY0MsZ0JBQWdCLEdBQUd0QywrQ0FBUUEsQ0FBQyxFQUFFO0lBQ25ELE1BQU0sQ0FBQ3VDLHFCQUFxQkMsdUJBQXVCLEdBQUd4QywrQ0FBUUEsQ0FBQyxFQUFFO0lBQ2pFLE1BQU15QyxTQUFTbEIsK0RBQWFBLENBQUM7SUFDN0IsTUFBTSxFQUFFbUIsaUJBQWlCLEVBQUVDLFNBQVNDLGlCQUFpQixFQUFFLEdBQ25EakIsK0VBQWlCQTtJQUVyQixNQUFNLENBQUNrQixtQkFBbUIsRUFBRUYsU0FBU0csbUJBQW1CLEVBQUUsQ0FBQyxHQUFHaEQsNkRBQVlBLENBQ3RFRixxRUFBaUJBLEVBQ2pCO1FBQ0ltRCxhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVkMsUUFBUUMsR0FBRyxDQUNQLHFEQUNBRixTQUFTRyxvQkFBb0I7WUFFakMsTUFBTUMsT0FBT0osU0FBU0csb0JBQW9CLENBQUNFLEtBQUs7WUFFaEQsNkVBQTZFO1lBQzdFLE1BQU1DLGtCQUFrQkYsS0FBS0csR0FBRyxDQUFDLENBQUNDO2dCQUM5QixNQUFNQyxxQkFBcUJoQixrQkFDdkJlLEtBQUtFLE1BQU0sQ0FBQ0MsRUFBRSxFQUNkSCxLQUFLRSxNQUFNO2dCQUdmLE9BQU87b0JBQ0gsR0FBR0YsSUFBSTtvQkFDUEUsUUFBUUQ7Z0JBQ1o7WUFDSjtZQUVBUixRQUFRQyxHQUFHLENBQ1Asb0RBQ0FJO1lBR0osSUFBSUEsaUJBQWlCO2dCQUNqQmpCLGdCQUFnQmlCO1lBQ3BCO1FBQ0o7UUFDQU0sU0FBUyxDQUFDQztZQUNOWixRQUFRWSxLQUFLLENBQUMsMkJBQTJCQTtRQUM3QztJQUNKO0lBR0osTUFBTUMsbUJBQW1CO1lBQ3JCQyw2RUFBb0IsR0FDcEJDLGdGQUFvQjtZQUFFLEdBQUdDLE1BQU07UUFBQztRQUVoQyxNQUFNckIsa0JBQWtCO1lBQ3BCc0IsV0FBVztnQkFDUEQsUUFBUUQ7Z0JBQ1JHLFFBQVFKLFlBQVk5QjtnQkFDcEJBLE9BQU9BO1lBQ1g7UUFDSjtJQUNKO0lBRUEsTUFBTW1DLDBCQUEwQixPQUFPSDtRQUNuQyxNQUFNSSxZQUFpQixDQUFDO1FBQ3hCLElBQUl4QyxXQUFXLEdBQUc7WUFDZHdDLFVBQVVDLFFBQVEsR0FBRztnQkFBRUMsSUFBSSxDQUFDMUM7WUFBUztRQUN6QztRQUNBLElBQUlDLFdBQVcsR0FBRztZQUNkdUMsVUFBVUcsUUFBUSxHQUFHO2dCQUFFRCxJQUFJLENBQUN6QztZQUFTO1FBQ3pDO1FBQ0EsSUFBSW1DLE9BQU9PLFFBQVEsRUFBRTtZQUNqQkgsVUFBVUcsUUFBUSxHQUFHUCxPQUFPTyxRQUFRO1FBQ3hDO1FBQ0EsSUFBSVAsT0FBT1EsYUFBYSxFQUFFO1lBQ3RCSixVQUFVSyxjQUFjLEdBQUc7Z0JBQUVILElBQUlOLE9BQU9RLGFBQWEsQ0FBQ2QsRUFBRSxDQUFDZ0IsUUFBUTtZQUFDO1FBQ3RFO1FBQ0EsSUFBSVYsT0FBT1csT0FBTyxFQUFFO1lBQ2hCUCxVQUFVQyxRQUFRLEdBQUc7Z0JBQUVDLElBQUlOLE9BQU9XLE9BQU8sQ0FBQ2pCLEVBQUUsQ0FBQ2dCLFFBQVE7WUFBQztRQUMxRDtRQUNBLElBQUlWLE9BQU8xRCxJQUFJLEVBQUU7WUFDYjhELFVBQVVRLE9BQU8sR0FBR1osT0FBTzFELElBQUk7UUFDbkMsT0FBTztZQUNIOEQsVUFBVVEsT0FBTyxHQUFHO2dCQUFFQyxJQUFJO1lBQUs7UUFDbkM7UUFDQSxNQUFNQyx3QkFBd0I7WUFDMUJiLFdBQVc7Z0JBQ1BELFFBQVFJO1lBQ1o7UUFDSjtJQUNKO0lBRUEsTUFBTSxFQUNGSixNQUFNLEVBQ05lLFNBQVMsRUFDVEMsa0JBQWtCLEVBQ3JCLEdBQUd6RCw4RUFBa0JBLENBQUM7UUFDbkIwRCxlQUFlLENBQUM7UUFDaEJDLFVBQVVyQjtRQUNWc0IsVUFBVWhCO1FBQ1ZpQixlQUFlLEtBQU87SUFDMUI7SUFFQXZGLGdEQUFTQSxDQUFDO1FBQ04sSUFBSWlDLGdCQUNBQSxlQUFldUQsT0FBTyxHQUFHO1lBQ3JCQyxPQUFPTjtZQUNQTyxTQUFTO1lBQ1RDLFlBQVksS0FBTztRQUN2QjtJQUNSLEdBQUc7UUFBQ1I7S0FBbUI7SUFFdkIsTUFBTSxDQUFDRix5QkFBeUIsRUFBRXJDLFNBQVNnRCwwQkFBMEIsRUFBRSxDQUFDLEdBQ3BFN0YsNkRBQVlBLENBQUNELDhFQUEwQkEsRUFBRTtRQUNyQ2tELGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWQyxRQUFRQyxHQUFHLENBQ1AseURBQ0FGLFNBQVMrQix1QkFBdUI7WUFFcEMsTUFBTTNCLE9BQU9KLFNBQVMrQix1QkFBdUIsQ0FBQzFCLEtBQUs7WUFDbkQsSUFBSUQsTUFBTTtnQkFDTixvRUFBb0U7Z0JBQ3BFLE1BQU11QyxlQUFldkMsS0FBS2EsTUFBTSxDQUFDLENBQUNULE9BQzlCQSxLQUFLRSxNQUFNLENBQUNrQyxjQUFjLENBQUN2QyxLQUFLLENBQUN3QyxJQUFJLENBQUMsQ0FBQ0M7d0JBQ25DLE9BQU9BLEVBQUVuQyxFQUFFLEtBQUtILEtBQUtjLFFBQVE7b0JBQ2pDO2dCQUVKLE1BQU15QixnQkFBZ0JKLGFBQWFwQyxHQUFHLENBQUMsQ0FBQ3lDO29CQUNwQyxPQUFPO3dCQUFFLEdBQUdBLEdBQUc7d0JBQUVDLFFBQVFoRywwRUFBd0JBLENBQUMrRjtvQkFBSztnQkFDM0Q7Z0JBQ0EsNENBQTRDO2dCQUM1QyxzREFBc0Q7Z0JBQ3RELHVCQUF1QjtnQkFDdkIsbUJBQW1CO2dCQUNuQix1Q0FBdUM7Z0JBQ3ZDLGtEQUFrRDtnQkFDbEQsMkRBQTJEO2dCQUMzRCxZQUFZO2dCQUNaLFNBQVM7Z0JBQ1QsSUFBSTtnQkFDSixvREFBb0Q7Z0JBQ3BELE1BQU1FLGNBQWNILGNBQWNJLE1BQU0sQ0FDcEMsQ0FBQ0MsS0FBVUo7b0JBQ1AsTUFBTUssTUFBTSxHQUFtQkwsT0FBaEJBLElBQUl4QixRQUFRLEVBQUMsS0FBeUJ3QixPQUF0QkEsSUFBSXRCLGNBQWMsRUFBQyxLQUFlLE9BQVpzQixJQUFJbkIsT0FBTztvQkFDaEUsSUFBSSxDQUFDdUIsR0FBRyxDQUFDQyxJQUFJLEVBQUU7d0JBQ1hELEdBQUcsQ0FBQ0MsSUFBSSxHQUFHOzRCQUNQMUMsSUFBSXFDLElBQUlyQyxFQUFFOzRCQUNWYSxVQUFVd0IsSUFBSXhCLFFBQVE7NEJBQ3RCZCxRQUFRc0MsSUFBSXRDLE1BQU07NEJBQ2xCZ0IsZ0JBQWdCc0IsSUFBSXRCLGNBQWM7NEJBQ2xDNEIsY0FBY04sSUFBSU0sWUFBWTs0QkFDOUJ6QixTQUFTbUIsSUFBSW5CLE9BQU87NEJBQ3BCb0IsUUFBUUQsSUFBSUMsTUFBTTs0QkFDbEJNLHNCQUNJUCxJQUFJUSxlQUFlLENBQ2RELG9CQUFvQjs0QkFDN0IzQixTQUFTLEVBQUU7d0JBQ2Y7b0JBQ0o7b0JBQ0F3QixHQUFHLENBQUNDLElBQUksQ0FBQ3pCLE9BQU8sQ0FBQzZCLElBQUksQ0FBQ1QsSUFBSVUsTUFBTTtvQkFDaEMsT0FBT047Z0JBQ1gsR0FDQSxDQUFDO2dCQUdMLE1BQU1PLGFBQWFDLE9BQU9DLE1BQU0sQ0FBQ1gsYUFBYTNDLEdBQUcsQ0FDN0MsQ0FBQ3VEO29CQUNHLE1BQU1DLGdCQUFnQkQsTUFBTWxDLE9BQU8sQ0FBQ3VCLE1BQU0sQ0FDdEMsQ0FBQ0MsS0FBVU07d0JBQ1AsTUFBTU0saUJBQWlCWixJQUFJYSxJQUFJLENBQzNCLENBQUNuQixJQUFXQSxFQUFFbkMsRUFBRSxLQUFLK0MsT0FBTy9DLEVBQUU7d0JBRWxDLElBQUlxRCxnQkFBZ0I7NEJBQ2hCQSxlQUFlRSxTQUFTLEdBQ3BCUixPQUFPUSxTQUFTOzRCQUNwQkYsZUFBZUcsT0FBTyxHQUFHVCxPQUFPUyxPQUFPO3dCQUMzQyxPQUFPOzRCQUNIZixJQUFJSyxJQUFJLENBQUNDO3dCQUNiO3dCQUNBLE9BQU9OO29CQUNYLEdBQ0EsRUFBRTtvQkFFTixPQUFPO3dCQUNIekMsSUFBSW1ELE1BQU1uRCxFQUFFO3dCQUNaYSxVQUFVc0MsTUFBTXRDLFFBQVE7d0JBQ3hCZCxRQUFRb0QsTUFBTXBELE1BQU07d0JBQ3BCZ0IsZ0JBQWdCb0MsTUFBTXBDLGNBQWM7d0JBQ3BDNEIsY0FBY1EsTUFBTVIsWUFBWTt3QkFDaENMLFFBQVFhLE1BQU1iLE1BQU07d0JBQ3BCcEIsU0FBU2lDLE1BQU1qQyxPQUFPO3dCQUN0QjBCLHNCQUNJTyxNQUFNUCxvQkFBb0I7d0JBQzlCM0IsU0FBU21DO29CQUNiO2dCQUNKO2dCQUVKOUQsUUFBUUMsR0FBRyxDQUNQLGlEQUNBeUQ7Z0JBRUpwRSx1QkFBdUJvRTtZQUMzQjtRQUNKO1FBQ0EvQyxTQUFTLENBQUNDO1lBQ05aLFFBQVFZLEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ25EO0lBQ0o7SUFFSi9ELGdEQUFTQSxDQUFDO1FBQ04sSUFBSW9DLFdBQVc7WUFDWCxNQUFNa0YsSUFBdUI7Z0JBQUUsR0FBR25ELE1BQU07WUFBQztZQUN6QyxJQUFJLENBQUNwQyxXQUFXLEdBQUc7Z0JBQ2Z1RixFQUFFeEMsT0FBTyxHQUFHO29CQUFFakIsSUFBSTt3QkFBRWdCLFVBQVUsQ0FBQzlDO29CQUFTO2dCQUFFO1lBQzlDO1lBQ0FtRCxVQUFVb0M7WUFDVmhELHdCQUF3QmdEO1lBQ3hCdEQsaUJBQWlCLEdBQUdzRDtZQUNwQmpGLGFBQWE7UUFDakI7SUFDSixHQUFHO1FBQUNEO0tBQVU7SUFFZCxNQUFNLENBQUNtRixhQUFhQyxlQUFlLEdBQUd2SCwrQ0FBUUEsQ0FBTTtJQUVwREQsZ0RBQVNBLENBQUM7UUFDTndILGVBQWVuSCxtRUFBY0E7SUFDakMsR0FBRyxFQUFFO0lBRUwsbUVBQW1FO0lBQ25FLE1BQU1vSCw4QkFBOEIsQ0FBQ25GO1FBQ2pDLE9BQU9BLGFBQWFtQixHQUFHLENBQUMsQ0FBQ2lFO2dCQUdqQkEsa0JBT1VBLG1CQUVNQSxnQ0FBQUEsK0JBQUFBLHlCQUNGQSxnQ0FBQUEsMEJBR0xBO1lBZmIscURBQXFEO1lBQ3JELE1BQU0vRCxxQkFBcUJoQixtQkFDdkIrRSxtQkFBQUEsU0FBUzlELE1BQU0sY0FBZjhELHVDQUFBQSxpQkFBaUI3RCxFQUFFLEVBQ25CNkQsU0FBUzlELE1BQU07WUFHbkIsT0FBTztnQkFDSEMsSUFBSTZELFNBQVM3RCxFQUFFO2dCQUNma0IsU0FBUzJDLFNBQVNqSCxJQUFJO2dCQUN0QmlFLFFBQVEsR0FBRWdELG9CQUFBQSxTQUFTOUQsTUFBTSxjQUFmOEQsd0NBQUFBLGtCQUFpQjdELEVBQUU7Z0JBQzdCRCxRQUFRRDtnQkFDUmlCLGNBQWMsR0FBRThDLDBCQUFBQSxTQUFTL0MsYUFBYSxjQUF0QitDLCtDQUFBQSxnQ0FBQUEsd0JBQXdCbkUsS0FBSyxjQUE3Qm1FLHFEQUFBQSxpQ0FBQUEsNkJBQStCLENBQUMsRUFBRSxjQUFsQ0EscURBQUFBLCtCQUFvQzdELEVBQUU7Z0JBQ3REMkMsY0FBY2tCLEVBQUFBLDJCQUFBQSxTQUFTL0MsYUFBYSxjQUF0QitDLGdEQUFBQSxpQ0FBQUEseUJBQXdCbkUsS0FBSyxjQUE3Qm1FLHFEQUFBQSw4QkFBK0IsQ0FBQyxFQUFFLEtBQUk7b0JBQ2hEQyxPQUFPO2dCQUNYO2dCQUNBN0MsU0FBUzRDLEVBQUFBLG9CQUFBQSxTQUFTNUMsT0FBTyxjQUFoQjRDLHdDQUFBQSxrQkFBa0JuRSxLQUFLLEtBQUksRUFBRTtnQkFDdENxRSxTQUFTRixTQUFTRSxPQUFPO2dCQUN6QnpCLFFBQVE7b0JBQ0owQixPQUFPO29CQUNQQyxXQUFXO29CQUNYQyxPQUFPO29CQUNQQyxvQkFBb0I7Z0JBQ3hCO2dCQUNBdkIsc0JBQXNCaUIsU0FBU2pCLG9CQUFvQjtnQkFDbkQsNkRBQTZEO2dCQUM3RHdCLFVBQVU7WUFDZDtRQUNKO0lBQ0o7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTUMsNEJBQTRCO1FBQzlCLGdDQUFnQztRQUNoQyxNQUFNQyx1QkFBdUJWLDRCQUN6Qm5GLGdCQUFnQixFQUFFO1FBR3RCLDZDQUE2QztRQUM3QyxNQUFNOEYsa0JBQWtCLENBQUM1Rix1QkFBdUIsRUFBRSxFQUFFaUIsR0FBRyxDQUFDLENBQUN5QztnQkFFM0NBLGFBRUpBO21CQUo2RDtnQkFDbkUsR0FBR0EsR0FBRztnQkFDTitCLFVBQVUvQixFQUFBQSxjQUFBQSxJQUFJQyxNQUFNLGNBQVZELGtDQUFBQSxZQUFZNEIsU0FBUyxJQUN6QixJQUNBNUIsRUFBQUEsZUFBQUEsSUFBSUMsTUFBTSxjQUFWRCxtQ0FBQUEsYUFBWThCLGtCQUFrQixJQUM1QixJQUNBO1lBQ1o7O1FBRUEsNEJBQTRCO1FBQzVCLE1BQU1LLGVBQWU7ZUFBSUQ7ZUFBb0JEO1NBQXFCO1FBRWxFLDZHQUE2RztRQUM3RyxPQUFPRSxhQUFhQyxJQUFJLENBQUMsQ0FBQ0MsR0FBUUM7WUFDOUIsa0VBQWtFO1lBQ2xFLElBQUlELEVBQUVOLFFBQVEsS0FBS08sRUFBRVAsUUFBUSxFQUFFO2dCQUMzQixPQUFPTyxFQUFFUCxRQUFRLEdBQUdNLEVBQUVOLFFBQVE7WUFDbEM7WUFFQSxxQ0FBcUM7WUFDckMsTUFBTVEsUUFBUSxJQUFJL0gsS0FBSzZILEVBQUV4RCxPQUFPLElBQUksR0FBRzJELE9BQU87WUFDOUMsTUFBTUMsUUFBUSxJQUFJakksS0FBSzhILEVBQUV6RCxPQUFPLElBQUksR0FBRzJELE9BQU87WUFFOUMsdUZBQXVGO1lBQ3ZGLElBQUlILEVBQUVOLFFBQVEsR0FBRyxHQUFHO2dCQUNoQixPQUFPUSxRQUFRRTtZQUNuQjtZQUVBLHNGQUFzRjtZQUN0RixPQUFPQSxRQUFRRjtRQUNuQjtJQUNKO0lBRUEsa0RBQWtEO0lBQ2xELE1BQU1HLHVCQUF1QixDQUN6QmxCO1FBRUEsSUFBSSxDQUFDQSxTQUFTdkIsTUFBTSxFQUFFLE9BQU87UUFFN0IsSUFBSXVCLFNBQVN2QixNQUFNLENBQUMyQixTQUFTLEVBQUU7WUFDM0IsT0FBTztRQUNYO1FBRUEsSUFBSUosU0FBU3ZCLE1BQU0sQ0FBQzZCLGtCQUFrQixFQUFFO1lBQ3BDLE9BQU87UUFDWDtRQUVBLE9BQU87SUFDWDtJQUVBLG9EQUFvRDtJQUNwRCxNQUFNYSwrQkFBK0IsSUFDakNqSSx3RUFBYUEsQ0FBQztZQUNWO2dCQUNJa0ksYUFBYTtnQkFDYkMsUUFBUTtnQkFDUkMsTUFBTTt3QkFBQyxFQUFFQyxHQUFHLEVBQWdCO3dCQU9adkI7b0JBTlosTUFBTUEsV0FBZ0J1QixJQUFJQyxRQUFRO29CQUNsQyxxQkFDSSw4REFBQ3JILGlGQUFrQkE7d0JBQ2Z5QixNQUFNb0U7d0JBQ04zRixVQUFVQTt3QkFDVm9ILE1BQ0l6QixFQUFBQSxtQkFBQUEsU0FBU3ZCLE1BQU0sY0FBZnVCLHVDQUFBQSxpQkFBaUJHLEtBQUssTUFBSyxjQUNyQixjQUNBOzs7Ozs7Z0JBSXRCO2dCQUNBdUIsV0FBVyxDQUFDQyxNQUFXQzt3QkFFREQsZ0JBQ0FDLGdCQU1KRCw2QkFBQUEsaUJBQ0FDLDZCQUFBQTtvQkFUZCxxREFBcUQ7b0JBQ3JELE1BQU1DLFlBQVlGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcscUNBQUFBLGVBQWdCcEIsUUFBUSxLQUFJO29CQUM5QyxNQUFNdUIsWUFBWUYsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUosUUFBUSxjQUFkSSxxQ0FBQUEsZUFBZ0JyQixRQUFRLEtBQUk7b0JBRTlDLElBQUlzQixjQUFjQyxXQUFXO3dCQUN6QixPQUFPQSxZQUFZRDtvQkFDdkI7b0JBRUEsTUFBTUUsUUFBUUosQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyx1Q0FBQUEsOEJBQUFBLGdCQUFnQjdDLFlBQVksY0FBNUI2QyxrREFBQUEsNEJBQThCMUIsS0FBSyxLQUFJO29CQUNyRCxNQUFNK0IsUUFBUUosQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUosUUFBUSxjQUFkSSx1Q0FBQUEsOEJBQUFBLGdCQUFnQjlDLFlBQVksY0FBNUI4QyxrREFBQUEsNEJBQThCM0IsS0FBSyxLQUFJO29CQUNyRCxPQUFPOEIsTUFBTUUsYUFBYSxDQUFDRDtnQkFDL0I7WUFDSjtZQUNBO2dCQUNJWixhQUFhO2dCQUNiYyxlQUFlO2dCQUNmYixRQUFRO3dCQUFDLEVBQUVjLE1BQU0sRUFBbUI7eUNBQ2hDLDhEQUFDOUksbUZBQW1CQTt3QkFDaEI4SSxRQUFRQTt3QkFDUmxDLE9BQU07Ozs7Ozs7Z0JBR2RtQyxZQUFZO2dCQUNaZCxNQUFNO3dCQUFDLEVBQUVDLEdBQUcsRUFBZ0I7d0JBR3BCdkIsd0JBQ0FBLCtCQUFBQSx5QkFTUUEsa0JBRUFBO29CQWRaLE1BQU1BLFdBQVd1QixJQUFJQyxRQUFRO29CQUM3QixNQUFNMUMsZUFDRmtCLEVBQUFBLHlCQUFBQSxTQUFTbEIsWUFBWSxjQUFyQmtCLDZDQUFBQSx1QkFBdUJDLEtBQUssT0FDNUJELDBCQUFBQSxTQUFTL0MsYUFBYSxjQUF0QitDLCtDQUFBQSxnQ0FBQUEsd0JBQXdCbkUsS0FBSyxjQUE3Qm1FLG9EQUFBQSw4QkFDTWpFLEdBQUcsQ0FBQyxDQUFDc0csSUFBV0EsRUFBRXBDLEtBQUssRUFDeEJxQyxJQUFJLENBQUMsVUFDVjtvQkFFSixxQkFDSSw4REFBQ0M7d0JBQ0dDLFdBQVd6SSxtREFBRUEsQ0FDVCx1QkFDQWlHLEVBQUFBLG1CQUFBQSxTQUFTdkIsTUFBTSxjQUFmdUIsdUNBQUFBLGlCQUFpQkksU0FBUyxLQUN0Qiw2Q0FDSkosRUFBQUEsb0JBQUFBLFNBQVN2QixNQUFNLGNBQWZ1Qix3Q0FBQUEsa0JBQWlCTSxrQkFBa0IsS0FDL0I7a0NBRVB4Qjs7Ozs7O2dCQUdiO2dCQUNBNEMsV0FBVyxDQUFDQyxNQUFXQzt3QkFDSkQsNkJBQUFBLGdCQUNBQyw2QkFBQUE7b0JBRGYsTUFBTWEsU0FBU2QsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyxzQ0FBQUEsOEJBQUFBLGVBQWdCN0MsWUFBWSxjQUE1QjZDLGtEQUFBQSw0QkFBOEIxQixLQUFLLEtBQUk7b0JBQ3RELE1BQU15QyxTQUFTZCxDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSixRQUFRLGNBQWRJLHNDQUFBQSw4QkFBQUEsZUFBZ0I5QyxZQUFZLGNBQTVCOEMsa0RBQUFBLDRCQUE4QjNCLEtBQUssS0FBSTtvQkFDdEQsT0FBT3dDLE9BQU9SLGFBQWEsQ0FBQ1M7Z0JBQ2hDO1lBQ0o7WUFDQTtnQkFDSXRCLGFBQWE7Z0JBQ2JjLGVBQWU7Z0JBQ2ZiLFFBQVE7d0JBQUMsRUFBRWMsTUFBTSxFQUFtQjt5Q0FDaEMsOERBQUM5SSxtRkFBbUJBO3dCQUFDOEksUUFBUUE7d0JBQVFsQyxPQUFNOzs7Ozs7O2dCQUUvQ21DLFlBQVk7Z0JBQ1pkLE1BQU07d0JBQUMsRUFBRUMsR0FBRyxFQUFnQjt3QkFLWHZCO29CQUpiLE1BQU1BLFdBQVd1QixJQUFJQyxRQUFRO29CQUM3QixxQkFDSSw4REFBQ2U7d0JBQUlDLFdBQVU7OzBDQUNYLDhEQUFDRztnQ0FBS0gsV0FBVTswQ0FDWHhDLEVBQUFBLG1CQUFBQSxTQUFTOUQsTUFBTSxjQUFmOEQsdUNBQUFBLGlCQUFpQkMsS0FBSyxLQUFJOzs7Ozs7MENBRS9CLDhEQUFDaEcseURBQWFBO2dDQUNWaUMsUUFBUThELFNBQVM5RCxNQUFNO2dDQUN2QjBHLGVBQWM7Ozs7Ozs7Ozs7OztnQkFJOUI7Z0JBQ0FsQixXQUFXLENBQUNDLE1BQVdDO3dCQUNKRCx1QkFBQUEsZ0JBQ0FDLHVCQUFBQTtvQkFEZixNQUFNYSxTQUFTZCxDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSCxRQUFRLGNBQWRHLHNDQUFBQSx3QkFBQUEsZUFBZ0J6RixNQUFNLGNBQXRCeUYsNENBQUFBLHNCQUF3QjFCLEtBQUssS0FBSTtvQkFDaEQsTUFBTXlDLFNBQVNkLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1KLFFBQVEsY0FBZEksc0NBQUFBLHdCQUFBQSxlQUFnQjFGLE1BQU0sY0FBdEIwRiw0Q0FBQUEsc0JBQXdCM0IsS0FBSyxLQUFJO29CQUNoRCxPQUFPd0MsT0FBT1IsYUFBYSxDQUFDUztnQkFDaEM7WUFDSjtZQUNBO2dCQUNJdEIsYUFBYTtnQkFDYmMsZUFBZTtnQkFDZmIsUUFBUTt3QkFBQyxFQUFFYyxNQUFNLEVBQW1CO3lDQUNoQyw4REFBQzlJLG1GQUFtQkE7d0JBQUM4SSxRQUFRQTt3QkFBUWxDLE9BQU07Ozs7Ozs7Z0JBRS9DbUMsWUFBWTtnQkFDWmQsTUFBTTt3QkFBQyxFQUFFQyxHQUFHLEVBQWdCO29CQUN4QixNQUFNdkIsV0FBV3VCLElBQUlDLFFBQVE7b0JBQzdCLE1BQU1wRSxVQUFVNEMsU0FBUzVDLE9BQU8sSUFBSSxFQUFFO29CQUV0QyxxQkFDSSw4REFBQ21GO3dCQUFJQyxXQUFVOzs0QkFDVnBGLFFBQVF5RixLQUFLLENBQUMsR0FBRyxHQUFHOUcsR0FBRyxDQUFDLENBQUNtRDtvQ0FNTmMsa0JBRU1BO29DQWViZDt1Q0F0QlQsOERBQUN2RixvREFBT0E7O3NEQUNKLDhEQUFDRSwyREFBY0E7c0RBQ1gsNEVBQUNOLG1EQUFNQTtnREFDSHVKLE1BQUs7Z0RBQ0xDLFNBQ0kvQyxFQUFBQSxtQkFBQUEsU0FBU3ZCLE1BQU0sY0FBZnVCLHVDQUFBQSxpQkFBaUJJLFNBQVMsSUFDcEIsZ0JBQ0FKLEVBQUFBLG9CQUFBQSxTQUFTdkIsTUFBTSxjQUFmdUIsd0NBQUFBLGtCQUNRTSxrQkFBa0IsSUFDeEIsWUFDQTswREFFWiw0RUFBQzlHLDJEQUFjQTtvREFBQ2dKLFdBQVU7OERBQ3JCOUksZ0VBQWVBLENBQ1p3RixPQUFPUSxTQUFTLEVBQ2hCUixPQUFPUyxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUs5Qiw4REFBQy9GLDJEQUFjQTs7Z0RBQ1ZzRixPQUFPUSxTQUFTO2dEQUFFO2dEQUNsQlIsQ0FBQUEsa0JBQUFBLE9BQU9TLE9BQU8sY0FBZFQsNkJBQUFBLGtCQUFrQjs7Ozs7Ozs7bUNBdEJiQSxPQUFPL0MsRUFBRTs7Ozs7NEJBd0JkOzRCQUVaaUIsUUFBUTRGLE1BQU0sR0FBRyxtQkFDZCw4REFBQ1Q7Z0NBQUlDLFdBQVU7O29DQUFxQztvQ0FDOUNwRixRQUFRNEYsTUFBTSxHQUFHOzs7Ozs7Ozs7Ozs7O2dCQUt2QztnQkFDQXRCLFdBQVcsQ0FBQ0MsTUFBV0M7d0JBRVpELHlCQUFBQSx3QkFBQUEsZ0JBQWlEQSwwQkFBQUEseUJBQUFBLGlCQUdqREMseUJBQUFBLHdCQUFBQSxnQkFBaURBLDBCQUFBQSx5QkFBQUE7d0JBSGpERCxtQ0FBaURBO29CQUR4RCxNQUFNYyxTQUNGLEdBQW9EZCxPQUFqREEsQ0FBQUEsb0NBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcsc0NBQUFBLHlCQUFBQSxlQUFnQnZFLE9BQU8sY0FBdkJ1RSw4Q0FBQUEsMEJBQUFBLHNCQUF5QixDQUFDLEVBQUUsY0FBNUJBLDhDQUFBQSx3QkFBOEJqQyxTQUFTLGNBQXZDaUMsK0NBQUFBLG9DQUEyQyxJQUFHLEtBQStDLE9BQTVDQSxDQUFBQSxrQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyx1Q0FBQUEsMEJBQUFBLGdCQUFnQnZFLE9BQU8sY0FBdkJ1RSwrQ0FBQUEsMkJBQUFBLHVCQUF5QixDQUFDLEVBQUUsY0FBNUJBLCtDQUFBQSx5QkFBOEJoQyxPQUFPLGNBQXJDZ0MsNkNBQUFBLGtDQUF5QyxPQUM3Rjt3QkFFR0MsbUNBQWlEQTtvQkFEeEQsTUFBTWMsU0FDRixHQUFvRGQsT0FBakRBLENBQUFBLG9DQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSixRQUFRLGNBQWRJLHNDQUFBQSx5QkFBQUEsZUFBZ0J4RSxPQUFPLGNBQXZCd0UsOENBQUFBLDBCQUFBQSxzQkFBeUIsQ0FBQyxFQUFFLGNBQTVCQSw4Q0FBQUEsd0JBQThCbEMsU0FBUyxjQUF2Q2tDLCtDQUFBQSxvQ0FBMkMsSUFBRyxLQUErQyxPQUE1Q0EsQ0FBQUEsa0NBQUFBLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU1KLFFBQVEsY0FBZEksdUNBQUFBLDBCQUFBQSxnQkFBZ0J4RSxPQUFPLGNBQXZCd0UsK0NBQUFBLDJCQUFBQSx1QkFBeUIsQ0FBQyxFQUFFLGNBQTVCQSwrQ0FBQUEseUJBQThCakMsT0FBTyxjQUFyQ2lDLDZDQUFBQSxrQ0FBeUMsT0FDN0Y7b0JBQ0osT0FBT2EsT0FBT1IsYUFBYSxDQUFDUztnQkFDaEM7WUFDSjtZQUNBO2dCQUNJdEIsYUFBYTtnQkFDYmMsZUFBZTtnQkFDZmIsUUFBUTt3QkFBQyxFQUFFYyxNQUFNLEVBQW1CO3lDQUNoQyw4REFBQzlJLG1GQUFtQkE7d0JBQUM4SSxRQUFRQTt3QkFBUWxDLE9BQU07Ozs7Ozs7Z0JBRS9DbUMsWUFBWTtnQkFDWmQsTUFBTTt3QkFBQyxFQUFFQyxHQUFHLEVBQWdCO3dCQU1adkIsa0JBRUFBLG1CQUVBQSxtQkFHSEE7b0JBWlQsTUFBTUEsV0FBV3VCLElBQUlDLFFBQVE7b0JBQzdCLHFCQUNJLDhEQUFDZTt3QkFDR0MsV0FBV3pJLG1EQUFFQSxDQUNULHdEQUNBaUcsRUFBQUEsbUJBQUFBLFNBQVN2QixNQUFNLGNBQWZ1Qix1Q0FBQUEsaUJBQWlCSSxTQUFTLEtBQ3RCLHNDQUNKSixFQUFBQSxvQkFBQUEsU0FBU3ZCLE1BQU0sY0FBZnVCLHdDQUFBQSxrQkFBaUJNLGtCQUFrQixLQUMvQiw4QkFDSk4sRUFBQUEsb0JBQUFBLFNBQVN2QixNQUFNLGNBQWZ1Qix3Q0FBQUEsa0JBQWlCRyxLQUFLLE1BQUssZUFDdkI7a0NBRVBILEVBQUFBLG9CQUFBQSxTQUFTdkIsTUFBTSxjQUFmdUIsd0NBQUFBLGtCQUFpQkcsS0FBSyxLQUFJOzs7Ozs7Z0JBR3ZDO2dCQUNBdUIsV0FBVyxDQUFDQyxNQUFXQzt3QkFDREQsZ0JBQ0FDO29CQURsQixNQUFNQyxZQUFZRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSCxRQUFRLGNBQWRHLHFDQUFBQSxlQUFnQnBCLFFBQVEsS0FBSTtvQkFDOUMsTUFBTXVCLFlBQVlGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1KLFFBQVEsY0FBZEkscUNBQUFBLGVBQWdCckIsUUFBUSxLQUFJO29CQUM5QyxPQUFPdUIsWUFBWUQ7Z0JBQ3ZCO1lBQ0o7WUFDQTtnQkFDSVQsYUFBYTtnQkFDYmMsZUFBZTtnQkFDZmIsUUFBUTt3QkFBQyxFQUFFYyxNQUFNLEVBQW1CO3lDQUNoQyw4REFBQzlJLG1GQUFtQkE7d0JBQUM4SSxRQUFRQTt3QkFBUWxDLE9BQU07Ozs7Ozs7Z0JBRS9DbUMsWUFBWTtnQkFDWmQsTUFBTTt3QkFBQyxFQUFFQyxHQUFHLEVBQWdCO3dCQVFadkIsa0JBRUFBO29CQVRaLE1BQU1BLFdBQVd1QixJQUFJQyxRQUFRO29CQUM3QixNQUFNeUIsV0FBV3BLLFdBQVdtSCxTQUFTM0MsT0FBTztvQkFFNUMscUJBQ0ksOERBQUNrRjt3QkFDR0MsV0FBV3pJLG1EQUFFQSxDQUNULGtDQUNBaUcsRUFBQUEsbUJBQUFBLFNBQVN2QixNQUFNLGNBQWZ1Qix1Q0FBQUEsaUJBQWlCSSxTQUFTLEtBQ3RCLDZDQUNKSixFQUFBQSxvQkFBQUEsU0FBU3ZCLE1BQU0sY0FBZnVCLHdDQUFBQSxrQkFBaUJNLGtCQUFrQixLQUMvQjtrQ0FFUDJDOzs7Ozs7Z0JBR2I7Z0JBQ0F2QixXQUFXLENBQUNDLE1BQVdDO3dCQUVmRCxnQkFHQUM7b0JBSkosTUFBTWIsUUFBUSxJQUFJL0gsS0FDZDJJLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcscUNBQUFBLGVBQWdCdEUsT0FBTyxLQUFJLEdBQzdCMkQsT0FBTztvQkFDVCxNQUFNQyxRQUFRLElBQUlqSSxLQUNkNEksQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUosUUFBUSxjQUFkSSxxQ0FBQUEsZUFBZ0J2RSxPQUFPLEtBQUksR0FDN0IyRCxPQUFPO29CQUNULE9BQU9DLFFBQVFGO2dCQUNuQjtZQUNKO1NBQ0g7SUFFTCxxREFBcUQ7SUFDckQsTUFBTW1DLHlCQUF5QjtRQUMzQixJQUFJMUksZUFBZTJJLFFBQVEsQ0FBQyxrQkFBa0I7WUFDMUMscUVBQXFFO1lBQ3JFLE1BQU1DLGNBQWM1QztZQUNwQi9FLFFBQVFDLEdBQUcsQ0FDUCx1REFDQTBIO1lBRUosT0FBT0E7UUFDWDtRQUNBLGtFQUFrRTtRQUNsRSxNQUFNQyxXQUFXdkksdUJBQXVCLEVBQUU7UUFDMUNXLFFBQVFDLEdBQUcsQ0FBQyxvREFBMEMySDtRQUN0RCxPQUFPQTtJQUNYO0lBRUEsSUFDSSxDQUFDeEQsZUFDQSxDQUFDakgsc0VBQWFBLENBQUMsaUJBQWlCaUgsZ0JBQzdCLENBQUNqSCxzRUFBYUEsQ0FBQyxpQkFBaUJpSCxnQkFDaEMsQ0FBQ2pILHNFQUFhQSxDQUFDLG1CQUFtQmlILGdCQUNsQyxDQUFDakgsc0VBQWFBLENBQUMsd0JBQXdCaUgsY0FDN0M7UUFDRSxPQUFPLENBQUNBLDRCQUNKLDhEQUFDNUcsb0RBQU9BOzs7O3NDQUVSLDhEQUFDQSxvREFBT0E7WUFBQ3FLLGNBQWE7Ozs7OztJQUU5QjtJQUVBLGdDQUFnQztJQUNoQyxNQUFNQyxzQkFBc0JMO0lBQzVCLE1BQU1NLGdCQUFnQm5JLHVCQUF1QjZDO0lBRTdDLHFCQUNJLDhEQUFDcUU7UUFBSUMsV0FBVTs7MEJBQ1gsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNYLDRFQUFDL0ksaURBQUlBOzhCQUNELDRFQUFDakIsa0VBQWtCQTt3QkFDZjZCLFVBQVVBO3dCQUNWb0osVUFBVWhHO3dCQUNWaUcsaUJBQWlCO3dCQUNqQmxKLGdCQUFnQkE7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLM0JnSiw4QkFDRyw4REFBQ2pCO2dCQUFJQyxXQUFVOztrQ0FDWCw4REFBQ2xKLG9GQUFPQTt3QkFBQ2tKLFdBQVU7Ozs7OztvQkFBOEI7Ozs7Ozs0QkFHckRlLENBQUFBLGdDQUFBQSwwQ0FBQUEsb0JBQXFCUCxNQUFNLElBQUcsa0JBQzlCLDhEQUFDNUosb0VBQWFBO2dCQUNWdUssU0FBU3hDO2dCQUNUdkYsTUFBTTJIO2dCQUNOSyxXQUFXMUM7Z0JBQ1gyQyxVQUFVO2dCQUNWQyxhQUFhOzs7OzswQ0FHakIsOERBQUN2QjtnQkFBSUMsV0FBVTswQkFDWCw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1gsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNYLDRFQUFDdUI7NEJBQUV2QixXQUFVO3NDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWpFO0dBbG5CTXBJOztRQWVhTiwyREFBYUE7UUFFeEJJLDJFQUFpQkE7UUFFeUM3Qix5REFBWUE7UUFxRnRFMkIsMEVBQWtCQTtRQWlCbEIzQix5REFBWUE7OztLQXpIZCtCO0FBb25CTiwrREFBZUEsZ0JBQWdCQSxFQUFBO0FBRXhCLE1BQU00SixzQkFBc0I7UUFBQyxFQUNoQ2xKLG1CQUFtQixFQUNuQm1KLGVBQWUsS0FBSyxFQUNwQkMsaUJBQWlCLEtBQUssRUFDdEJMLFdBQVcsRUFBRSxFQUNYOztJQUNGLE1BQU03SSxTQUFTbEIsK0RBQWFBLENBQUM7SUFFN0IsTUFBTXFLLGFBQWFqTCx3RUFBYUEsQ0FBQztRQUM3QjtZQUNJa0ksYUFBYTtZQUNiQyxRQUFRO1lBQ1JDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTTNGLE9BQVkyRixJQUFJQyxRQUFRO2dCQUM5QixxQkFBTyw4REFBQ3JILGlGQUFrQkE7b0JBQUN5QixNQUFNQTtvQkFBTTZGLE1BQUs7Ozs7OztZQUNoRDtRQUNKO1FBQ0E7WUFDSUwsYUFBYTtZQUNiYyxlQUFlO1lBQ2ZiLFFBQVE7WUFDUmUsWUFBWTtZQUNaZCxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7b0JBTVAvQztnQkFMakIsTUFBTUEsTUFBTStDLElBQUlDLFFBQVE7Z0JBQ3hCLHFCQUNJOzhCQUNLeUMsZ0JBQWdCLHVCQUNiLDhEQUFDMUI7d0JBQUlDLFdBQVU7a0NBQ1ZoRSxFQUFBQSxjQUFBQSxJQUFJdEMsTUFBTSxjQUFWc0Msa0NBQUFBLFlBQVl5QixLQUFLLEtBQUk7Ozs7Ozs7WUFLMUM7WUFDQXlCLFdBQVcsQ0FBQ0MsTUFBV0M7b0JBQ0pELHVCQUFBQSxnQkFDQUMsdUJBQUFBO2dCQURmLE1BQU1hLFNBQVNkLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcsc0NBQUFBLHdCQUFBQSxlQUFnQnpGLE1BQU0sY0FBdEJ5Riw0Q0FBQUEsc0JBQXdCMUIsS0FBSyxLQUFJO2dCQUNoRCxNQUFNeUMsU0FBU2QsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUosUUFBUSxjQUFkSSxzQ0FBQUEsd0JBQUFBLGVBQWdCMUYsTUFBTSxjQUF0QjBGLDRDQUFBQSxzQkFBd0IzQixLQUFLLEtBQUk7Z0JBQ2hELE9BQU93QyxPQUFPUixhQUFhLENBQUNTO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJdEIsYUFBYTtZQUNiYyxlQUFlO1lBQ2ZiLFFBQVE7WUFDUmUsWUFBWTtZQUNaZCxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7b0JBcUNaL0M7Z0JBcENaLE1BQU1BLE1BQU0rQyxJQUFJQyxRQUFRO2dCQUN4QixNQUFNcEUsVUFBVW9CLElBQUlwQixPQUFPLElBQUksRUFBRTtnQkFFakMsT0FBT3BDLHVCQUNILDhEQUFDdUg7b0JBQUlDLFdBQVU7OEJBQ1ZwRixRQUFRckIsR0FBRyxDQUFDLENBQUNtRDs0QkFxQkdBO3dCQXBCYixxQkFDSSw4REFBQ3ZGLG9EQUFPQTs7OENBQ0osOERBQUNFLDJEQUFjQTs4Q0FDWCw0RUFBQ04sbURBQU1BO3dDQUNIdUosTUFBSzt3Q0FDTEMsU0FDSXZFLElBQUlDLE1BQU0sQ0FBQzJCLFNBQVMsR0FDZCxnQkFDQTtrREFFViw0RUFBQzVHLDJEQUFjQTs0Q0FBQ2dKLFdBQVU7c0RBQ3JCOUksZ0VBQWVBLENBQ1p3RixPQUFPUSxTQUFTLEVBQ2hCUixPQUFPUyxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7OzhDQUs5Qiw4REFBQy9GLDJEQUFjQTs7d0NBQ1ZzRixPQUFPUSxTQUFTO3dDQUFFO3dDQUNsQlIsQ0FBQUEsa0JBQUFBLE9BQU9TLE9BQU8sY0FBZFQsNkJBQUFBLGtCQUFrQjs7Ozs7Ozs7MkJBbkJiQSxPQUFPL0MsRUFBRTs7Ozs7b0JBdUIvQjs7Ozs7OENBR0osOERBQUNvRztvQkFDR0MsV0FBV3pJLG1EQUFFQSxDQUNULDBCQUNBeUUsY0FBQUEsSUFBSUMsTUFBTSxjQUFWRCxrQ0FBQUEsWUFBWTZCLEtBQUs7OEJBRXBCakQsUUFBUTRGLE1BQU07Ozs7OztZQUczQjtZQUNBdEIsV0FBVyxDQUFDQyxNQUFXQztvQkFFWkQsK0JBQUFBLDhCQUFBQSx3QkFBQUEsZ0JBQXdEQSxnQ0FBQUEsK0JBQUFBLHlCQUFBQSxpQkFHeERDLCtCQUFBQSw4QkFBQUEsd0JBQUFBLGdCQUF3REEsZ0NBQUFBLCtCQUFBQSx5QkFBQUE7b0JBSHhERCx5Q0FBd0RBO2dCQUQvRCxNQUFNYyxTQUNGLEdBQTJEZCxPQUF4REEsQ0FBQUEsMENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcsc0NBQUFBLHlCQUFBQSxlQUFnQnZFLE9BQU8sY0FBdkJ1RSw4Q0FBQUEsK0JBQUFBLHVCQUF5QjlGLEtBQUssY0FBOUI4RixvREFBQUEsZ0NBQUFBLDRCQUFnQyxDQUFDLEVBQUUsY0FBbkNBLG9EQUFBQSw4QkFBcUNqQyxTQUFTLGNBQTlDaUMscURBQUFBLDBDQUFrRCxJQUFHLEtBQXNELE9BQW5EQSxDQUFBQSx3Q0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyx1Q0FBQUEsMEJBQUFBLGdCQUFnQnZFLE9BQU8sY0FBdkJ1RSwrQ0FBQUEsZ0NBQUFBLHdCQUF5QjlGLEtBQUssY0FBOUI4RixxREFBQUEsaUNBQUFBLDZCQUFnQyxDQUFDLEVBQUUsY0FBbkNBLHFEQUFBQSwrQkFBcUNoQyxPQUFPLGNBQTVDZ0MsbURBQUFBLHdDQUFnRCxPQUMzRztvQkFFR0MseUNBQXdEQTtnQkFEL0QsTUFBTWMsU0FDRixHQUEyRGQsT0FBeERBLENBQUFBLDBDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSixRQUFRLGNBQWRJLHNDQUFBQSx5QkFBQUEsZUFBZ0J4RSxPQUFPLGNBQXZCd0UsOENBQUFBLCtCQUFBQSx1QkFBeUIvRixLQUFLLGNBQTlCK0Ysb0RBQUFBLGdDQUFBQSw0QkFBZ0MsQ0FBQyxFQUFFLGNBQW5DQSxvREFBQUEsOEJBQXFDbEMsU0FBUyxjQUE5Q2tDLHFEQUFBQSwwQ0FBa0QsSUFBRyxLQUFzRCxPQUFuREEsQ0FBQUEsd0NBQUFBLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU1KLFFBQVEsY0FBZEksdUNBQUFBLDBCQUFBQSxnQkFBZ0J4RSxPQUFPLGNBQXZCd0UsK0NBQUFBLGdDQUFBQSx3QkFBeUIvRixLQUFLLGNBQTlCK0YscURBQUFBLGlDQUFBQSw2QkFBZ0MsQ0FBQyxFQUFFLGNBQW5DQSxxREFBQUEsK0JBQXFDakMsT0FBTyxjQUE1Q2lDLG1EQUFBQSx3Q0FBZ0QsT0FDM0c7Z0JBQ0osT0FBT2EsT0FBT1IsYUFBYSxDQUFDUztZQUNoQztRQUNKO1FBQ0E7WUFDSXRCLGFBQWE7WUFDYmMsZUFBZTtZQUNmYixRQUFRO1lBQ1JlLFlBQVk7WUFDWmQsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO29CQUlGL0MsYUFBd0JBLGNBQ3JDQTtnQkFKVCxNQUFNQSxNQUFNK0MsSUFBSUMsUUFBUTtnQkFDeEIscUJBQ0ksOERBQUNlO29CQUNHQyxXQUFXLEdBQWtELE9BQS9DaEUsRUFBQUEsY0FBQUEsSUFBSUMsTUFBTSxjQUFWRCxrQ0FBQUEsWUFBWTRCLFNBQVMsS0FBRzVCLGVBQUFBLElBQUlDLE1BQU0sY0FBVkQsbUNBQUFBLGFBQVk2QixLQUFLLEdBQUcsSUFBRzs4QkFDNUQ3QixFQUFBQSxlQUFBQSxJQUFJQyxNQUFNLGNBQVZELG1DQUFBQSxhQUFZMkIsS0FBSyxLQUFJOzs7Ozs7WUFHbEM7UUFDSjtLQUNIO0lBRUQscURBQXFEO0lBQ3JELE1BQU13RCxVQUFVTyxpQkFDVkMsV0FBVzFILE1BQU0sQ0FBQyxDQUFDMkgsTUFBYUEsSUFBSWhELFdBQVcsS0FBSyxVQUNwRCtDO0lBRU4scUJBQ0k7a0JBQ0tySixDQUFBQSxnQ0FBQUEsMENBQUFBLG9CQUFxQmtJLE1BQU0sSUFBRyxrQkFDM0IsOERBQUM3SixnRUFBU0E7WUFDTndLLFNBQVNBO1lBQ1QvSCxNQUFNZDtZQUNOK0ksVUFBVUE7WUFDVkMsYUFBYTs7Ozs7c0NBR2pCLDhEQUFDdkI7WUFBSUMsV0FBWTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ1gsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNYLDRFQUFDdUI7d0JBQUV2QixXQUFVO2tDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVTlDLEVBQUM7SUEvSVl3Qjs7UUFNTWxLLDJEQUFhQTs7O01BTm5Ca0siLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC91aS9jcmV3LXRyYWluaW5nL2xpc3QudHN4PzRlNjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQge1xyXG4gICAgVFJBSU5JTkdfU0VTU0lPTlMsXHJcbiAgICBSRUFEX1RSQUlOSU5HX1NFU1NJT05fRFVFUyxcclxufSBmcm9tICdAL2FwcC9saWIvZ3JhcGhRTC9xdWVyeSdcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnXHJcbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcclxuaW1wb3J0IHsgVHJhaW5pbmdMaXN0RmlsdGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2ZpbHRlcidcclxuaW1wb3J0IHsgR2V0VHJhaW5pbmdTZXNzaW9uU3RhdHVzIH0gZnJvbSAnQC9hcHAvbGliL2FjdGlvbnMnXHJcbmltcG9ydCB7IGZvcm1hdCB9IGZyb20gJ2RhdGUtZm5zJ1xyXG5pbXBvcnQgeyBnZXRQZXJtaXNzaW9ucywgaGFzUGVybWlzc2lvbiB9IGZyb20gJ0AvYXBwL2hlbHBlcnMvdXNlckhlbHBlcidcclxuXHJcbi8vIEhlbHBlciBmdW5jdGlvbiB0byBmb3JtYXQgZGF0ZXMgdXNpbmcgZGF0ZS1mbnNcclxuY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlU3RyaW5nOiBhbnkpID0+IHtcclxuICAgIGlmICghZGF0ZVN0cmluZykgcmV0dXJuICcnXHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKVxyXG4gICAgICAgIHJldHVybiBmb3JtYXQoZGF0ZSwgJ2RkL01NL3l5JylcclxuICAgIH0gY2F0Y2gge1xyXG4gICAgICAgIHJldHVybiAnJ1xyXG4gICAgfVxyXG59XHJcbmltcG9ydCBMb2FkaW5nIGZyb20gJ0AvYXBwL2xvYWRpbmcnXHJcblxyXG5pbXBvcnQge1xyXG4gICAgY3JlYXRlQ29sdW1ucyxcclxuICAgIERhdGFUYWJsZSxcclxuICAgIEV4dGVuZGVkQ29sdW1uRGVmLFxyXG4gICAgRmlsdGVyZWRUYWJsZSxcclxufSBmcm9tICdAL2NvbXBvbmVudHMvZmlsdGVyZWRUYWJsZSdcclxuaW1wb3J0IHsgRGF0YVRhYmxlU29ydEhlYWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9kYXRhLXRhYmxlLXNvcnQtaGVhZGVyJ1xyXG5pbXBvcnQgeyBMb2FkZXIyIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xyXG5pbXBvcnQge1xyXG4gICAgQXZhdGFyLFxyXG4gICAgQXZhdGFyRmFsbGJhY2ssXHJcbiAgICBDYXJkLFxyXG4gICAgZ2V0Q3Jld0luaXRpYWxzLFxyXG4gICAgVG9vbHRpcCxcclxuICAgIFRvb2x0aXBDb250ZW50LFxyXG4gICAgVG9vbHRpcFRyaWdnZXIsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpJ1xyXG5pbXBvcnQgeyB1c2VNZWRpYVF1ZXJ5IH0gZnJvbSAnQHJlYWN0dXNlcy9jb3JlJ1xyXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvYXBwL2xpYi91dGlscydcclxuaW1wb3J0IHsgdXNlVHJhaW5pbmdGaWx0ZXJzIH0gZnJvbSAnLi9ob29rcy91c2VUcmFpbmluZ0ZpbHRlcnMnXHJcbmltcG9ydCB7IExvY2F0aW9uTW9kYWwgfSBmcm9tICcuLi92ZXNzZWxzL2xpc3QnXHJcbmltcG9ydCB7IHVzZVZlc3NlbEljb25EYXRhIH0gZnJvbSAnQC9hcHAvbGliL3Zlc3NlbC1pY29uLWhlbHBlcidcclxuXHJcbmltcG9ydCB7IE1vYmlsZVRyYWluaW5nQ2FyZCB9IGZyb20gJy4vY29tcG9uZW50cy9tb2JpbGUtdHJhaW5pbmctY2FyZCdcclxuXHJcbmNvbnN0IENyZXdUcmFpbmluZ0xpc3QgPSAoe1xyXG4gICAgbWVtYmVySWQgPSAwLFxyXG4gICAgdmVzc2VsSWQgPSAwLFxyXG4gICAgYXBwbHlGaWx0ZXJSZWYsXHJcbiAgICBleGNsdWRlRmlsdGVycyA9IFtdLFxyXG59OiB7XHJcbiAgICBtZW1iZXJJZD86IG51bWJlclxyXG4gICAgdmVzc2VsSWQ/OiBudW1iZXJcclxuICAgIGFwcGx5RmlsdGVyUmVmPzogYW55XHJcbiAgICBleGNsdWRlRmlsdGVycz86IHN0cmluZ1tdXHJcbn0pID0+IHtcclxuICAgIGNvbnN0IGxpbWl0ID0gMTAwXHJcbiAgICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcclxuICAgIGNvbnN0IFt0cmFpbmluZ0xpc3QsIHNldFRyYWluaW5nTGlzdF0gPSB1c2VTdGF0ZShbXSBhcyBhbnkpXHJcbiAgICBjb25zdCBbdHJhaW5pbmdTZXNzaW9uRHVlcywgc2V0VHJhaW5pbmdTZXNzaW9uRHVlc10gPSB1c2VTdGF0ZShbXSBhcyBhbnkpXHJcbiAgICBjb25zdCBpc1dpZGUgPSB1c2VNZWRpYVF1ZXJ5KCcobWluLXdpZHRoOiA3MjBweCknKVxyXG4gICAgY29uc3QgeyBnZXRWZXNzZWxXaXRoSWNvbiwgbG9hZGluZzogdmVzc2VsRGF0YUxvYWRpbmcgfSA9XHJcbiAgICAgICAgdXNlVmVzc2VsSWNvbkRhdGEoKVxyXG5cclxuICAgIGNvbnN0IFtxdWVyeVRyYWluaW5nTGlzdCwgeyBsb2FkaW5nOiB0cmFpbmluZ0xpc3RMb2FkaW5nIH1dID0gdXNlTGF6eVF1ZXJ5KFxyXG4gICAgICAgIFRSQUlOSU5HX1NFU1NJT05TLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgICAgICAgJ/CflI0gUmF3IHRyYWluaW5nIHNlc3Npb25zIGRhdGEgZnJvbSBBUEk6JyxcclxuICAgICAgICAgICAgICAgICAgICByZXNwb25zZS5yZWFkVHJhaW5pbmdTZXNzaW9ucyxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkVHJhaW5pbmdTZXNzaW9ucy5ub2Rlc1xyXG5cclxuICAgICAgICAgICAgICAgIC8vIFRyYW5zZm9ybSB2ZXNzZWwgZGF0YSB0byBpbmNsdWRlIGNvbXBsZXRlIHZlc3NlbCBpbmZvcm1hdGlvbiB3aXRoIHBvc2l0aW9uXHJcbiAgICAgICAgICAgICAgICBjb25zdCB0cmFuc2Zvcm1lZERhdGEgPSBkYXRhLm1hcCgoaXRlbTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY29tcGxldGVWZXNzZWxEYXRhID0gZ2V0VmVzc2VsV2l0aEljb24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0udmVzc2VsLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpdGVtLnZlc3NlbCxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLml0ZW0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbDogY29tcGxldGVWZXNzZWxEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgICAgICAgJ/CflIQgVHJhbnNmb3JtZWQgdHJhaW5pbmcgc2Vzc2lvbnMgZGF0YTonLFxyXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybWVkRGF0YSxcclxuICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAodHJhbnNmb3JtZWREYXRhKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdMaXN0KHRyYW5zZm9ybWVkRGF0YSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5VHJhaW5pbmdMaXN0IGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgIClcclxuXHJcbiAgICBjb25zdCBsb2FkVHJhaW5pbmdMaXN0ID0gYXN5bmMgKFxyXG4gICAgICAgIHN0YXJ0UGFnZTogbnVtYmVyID0gMCxcclxuICAgICAgICBzZWFyY2hGaWx0ZXI6IGFueSA9IHsgLi4uZmlsdGVyIH0sXHJcbiAgICApID0+IHtcclxuICAgICAgICBhd2FpdCBxdWVyeVRyYWluaW5nTGlzdCh7XHJcbiAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgZmlsdGVyOiBzZWFyY2hGaWx0ZXIsXHJcbiAgICAgICAgICAgICAgICBvZmZzZXQ6IHN0YXJ0UGFnZSAqIGxpbWl0LFxyXG4gICAgICAgICAgICAgICAgbGltaXQ6IGxpbWl0LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMgPSBhc3luYyAoZmlsdGVyOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zdCBkdWVGaWx0ZXI6IGFueSA9IHt9XHJcbiAgICAgICAgaWYgKG1lbWJlcklkID4gMCkge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIubWVtYmVySUQgPSB7IGVxOiArbWVtYmVySWQgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodmVzc2VsSWQgPiAwKSB7XHJcbiAgICAgICAgICAgIGR1ZUZpbHRlci52ZXNzZWxJRCA9IHsgZXE6ICt2ZXNzZWxJZCB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChmaWx0ZXIudmVzc2VsSUQpIHtcclxuICAgICAgICAgICAgZHVlRmlsdGVyLnZlc3NlbElEID0gZmlsdGVyLnZlc3NlbElEXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChmaWx0ZXIudHJhaW5pbmdUeXBlcykge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIudHJhaW5pbmdUeXBlSUQgPSB7IGVxOiBmaWx0ZXIudHJhaW5pbmdUeXBlcy5pZC5jb250YWlucyB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChmaWx0ZXIubWVtYmVycykge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIubWVtYmVySUQgPSB7IGVxOiBmaWx0ZXIubWVtYmVycy5pZC5jb250YWlucyB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChmaWx0ZXIuZGF0ZSkge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIuZHVlRGF0ZSA9IGZpbHRlci5kYXRlXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgZHVlRmlsdGVyLmR1ZURhdGUgPSB7IG5lOiBudWxsIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgYXdhaXQgcmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMoe1xyXG4gICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgIGZpbHRlcjogZHVlRmlsdGVyLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3Qge1xyXG4gICAgICAgIGZpbHRlcixcclxuICAgICAgICBzZXRGaWx0ZXIsIC8vIHVzZWQgaW4gdGhlIHN0YXJ0dXAgZWZmZWN0XHJcbiAgICAgICAgaGFuZGxlRmlsdGVyQ2hhbmdlLCAvLyByZXBsYWNlcyB0aGUgb2xkIGhhbmRsZUZpbHRlck9uQ2hhbmdlXHJcbiAgICB9ID0gdXNlVHJhaW5pbmdGaWx0ZXJzKHtcclxuICAgICAgICBpbml0aWFsRmlsdGVyOiB7fSxcclxuICAgICAgICBsb2FkTGlzdDogbG9hZFRyYWluaW5nTGlzdCxcclxuICAgICAgICBsb2FkRHVlczogbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMsXHJcbiAgICAgICAgdG9nZ2xlT3ZlcmR1ZTogKCkgPT4ge30sIC8vIE5vLW9wIGZ1bmN0aW9uIHNpbmNlIHRvZ2dsZSBpcyByZW1vdmVkXHJcbiAgICB9KVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKGFwcGx5RmlsdGVyUmVmKVxyXG4gICAgICAgICAgICBhcHBseUZpbHRlclJlZi5jdXJyZW50ID0ge1xyXG4gICAgICAgICAgICAgICAgYXBwbHk6IGhhbmRsZUZpbHRlckNoYW5nZSwgLy8gdHJpZ2dlciBpdFxyXG4gICAgICAgICAgICAgICAgb3ZlcmR1ZTogZmFsc2UsIC8vIEFsd2F5cyBmYWxzZSBzaW5jZSB0b2dnbGUgaXMgcmVtb3ZlZFxyXG4gICAgICAgICAgICAgICAgc2V0T3ZlcmR1ZTogKCkgPT4ge30sIC8vIE5vLW9wIGZ1bmN0aW9uIHNpbmNlIHRvZ2dsZSBpcyByZW1vdmVkXHJcbiAgICAgICAgICAgIH1cclxuICAgIH0sIFtoYW5kbGVGaWx0ZXJDaGFuZ2VdKVxyXG5cclxuICAgIGNvbnN0IFtyZWFkVHJhaW5pbmdTZXNzaW9uRHVlcywgeyBsb2FkaW5nOiB0cmFpbmluZ1Nlc3Npb25EdWVzTG9hZGluZyB9XSA9XHJcbiAgICAgICAgdXNlTGF6eVF1ZXJ5KFJFQURfVFJBSU5JTkdfU0VTU0lPTl9EVUVTLCB7XHJcbiAgICAgICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgICAgICAgICAgICAgICfwn5SNIFJhdyB0cmFpbmluZyBzZXNzaW9uIGR1ZXMgZGF0YSBmcm9tIEFQSTonLFxyXG4gICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLnJlYWRUcmFpbmluZ1Nlc3Npb25EdWVzLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnJlYWRUcmFpbmluZ1Nlc3Npb25EdWVzLm5vZGVzXHJcbiAgICAgICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIEZpbHRlciBvdXQgY3JldyBtZW1iZXJzIHdobyBhcmUgbm8gbG9uZ2VyIGFzc2lnbmVkIHRvIHRoZSB2ZXNzZWwuXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsdGVyZWREYXRhID0gZGF0YS5maWx0ZXIoKGl0ZW06IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS52ZXNzZWwuc2VhTG9nc01lbWJlcnMubm9kZXMuc29tZSgobTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gbS5pZCA9PT0gaXRlbS5tZW1iZXJJRFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZHVlV2l0aFN0YXR1cyA9IGZpbHRlcmVkRGF0YS5tYXAoKGR1ZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7IC4uLmR1ZSwgc3RhdHVzOiBHZXRUcmFpbmluZ1Nlc3Npb25TdGF0dXMoZHVlKSB9XHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAvLyBSZXR1cm4gb25seSBkdWUgd2l0aGluIDcgZGF5cyBhbmQgb3ZlcmR1ZVxyXG4gICAgICAgICAgICAgICAgICAgIC8vIGNvbnN0IGZpbHRlcmVkRHVlV2l0aFN0YXR1cyA9IGR1ZVdpdGhTdGF0dXMuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAgIC8vICAgICAoaXRlbTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIC8vICAgICAgICAgICAgIGl0ZW0uc3RhdHVzLmlzT3ZlcmR1ZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgIC8vICAgICAgICAgICAgIChpdGVtLnN0YXR1cy5pc092ZXJkdWUgPT09IGZhbHNlICYmXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gICAgICAgICAgICAgICAgIGl0ZW0uc3RhdHVzLmR1ZVdpdGhpblNldmVuRGF5cyA9PT0gdHJ1ZSlcclxuICAgICAgICAgICAgICAgICAgICAvLyAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAvLyAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAvLyApXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gY29uc3QgZ3JvdXBlZER1ZXMgPSBmaWx0ZXJlZER1ZVdpdGhTdGF0dXMucmVkdWNlKFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGdyb3VwZWREdWVzID0gZHVlV2l0aFN0YXR1cy5yZWR1Y2UoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChhY2M6IGFueSwgZHVlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGtleSA9IGAke2R1ZS52ZXNzZWxJRH0tJHtkdWUudHJhaW5pbmdUeXBlSUR9LSR7ZHVlLmR1ZURhdGV9YFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFhY2Nba2V5XSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjY1trZXldID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogZHVlLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxJRDogZHVlLnZlc3NlbElELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw6IGR1ZS52ZXNzZWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZUlEOiBkdWUudHJhaW5pbmdUeXBlSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZTogZHVlLnRyYWluaW5nVHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVlRGF0ZTogZHVlLmR1ZURhdGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogZHVlLnN0YXR1cyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdMb2NhdGlvblR5cGU6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdWUudHJhaW5pbmdTZXNzaW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLnRyYWluaW5nTG9jYXRpb25UeXBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJzOiBbXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY2Nba2V5XS5tZW1iZXJzLnB1c2goZHVlLm1lbWJlcilcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBhY2NcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAge30sXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXJnZWREdWVzID0gT2JqZWN0LnZhbHVlcyhncm91cGVkRHVlcykubWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAoZ3JvdXA6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbWVyZ2VkTWVtYmVycyA9IGdyb3VwLm1lbWJlcnMucmVkdWNlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChhY2M6IGFueSwgbWVtYmVyOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdNZW1iZXIgPSBhY2MuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChtOiBhbnkpID0+IG0uaWQgPT09IG1lbWJlci5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZXhpc3RpbmdNZW1iZXIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXN0aW5nTWVtYmVyLmZpcnN0TmFtZSA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLmZpcnN0TmFtZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIuc3VybmFtZSA9IG1lbWJlci5zdXJuYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY2MucHVzaChtZW1iZXIpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFjY1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW10sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBncm91cC5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxJRDogZ3JvdXAudmVzc2VsSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsOiBncm91cC52ZXNzZWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IGdyb3VwLnRyYWluaW5nVHlwZUlELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZTogZ3JvdXAudHJhaW5pbmdUeXBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogZ3JvdXAuc3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1ZURhdGU6IGdyb3VwLmR1ZURhdGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdMb2NhdGlvblR5cGU6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyb3VwLnRyYWluaW5nTG9jYXRpb25UeXBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcnM6IG1lcmdlZE1lbWJlcnMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAn8J+UhCBQcm9jZXNzZWQgdHJhaW5pbmcgc2Vzc2lvbiBkdWVzOicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lcmdlZER1ZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldFRyYWluaW5nU2Vzc2lvbkR1ZXMobWVyZ2VkRHVlcylcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3JlYWRUcmFpbmluZ1Nlc3Npb25EdWVzIGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChpc0xvYWRpbmcpIHtcclxuICAgICAgICAgICAgY29uc3QgZjogeyBtZW1iZXJzPzogYW55IH0gPSB7IC4uLmZpbHRlciB9XHJcbiAgICAgICAgICAgIGlmICgrbWVtYmVySWQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICBmLm1lbWJlcnMgPSB7IGlkOiB7IGNvbnRhaW5zOiArbWVtYmVySWQgfSB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgc2V0RmlsdGVyKGYpXHJcbiAgICAgICAgICAgIGxvYWRUcmFpbmluZ1Nlc3Npb25EdWVzKGYpXHJcbiAgICAgICAgICAgIGxvYWRUcmFpbmluZ0xpc3QoMCwgZilcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtpc0xvYWRpbmddKVxyXG5cclxuICAgIGNvbnN0IFtwZXJtaXNzaW9ucywgc2V0UGVybWlzc2lvbnNdID0gdXNlU3RhdGU8YW55PihmYWxzZSlcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIHNldFBlcm1pc3Npb25zKGdldFBlcm1pc3Npb25zKVxyXG4gICAgfSwgW10pXHJcblxyXG4gICAgLy8gVHJhbnNmb3JtIGNvbXBsZXRlZCB0cmFpbmluZyBzZXNzaW9ucyB0byBtYXRjaCB1bmlmaWVkIHN0cnVjdHVyZVxyXG4gICAgY29uc3QgdHJhbnNmb3JtQ29tcGxldGVkVHJhaW5pbmdzID0gKHRyYWluaW5nTGlzdDogYW55W10pID0+IHtcclxuICAgICAgICByZXR1cm4gdHJhaW5pbmdMaXN0Lm1hcCgodHJhaW5pbmc6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAvLyBFbnN1cmUgdmVzc2VsIGhhcyBjb21wbGV0ZSBkYXRhIGluY2x1ZGluZyBwb3NpdGlvblxyXG4gICAgICAgICAgICBjb25zdCBjb21wbGV0ZVZlc3NlbERhdGEgPSBnZXRWZXNzZWxXaXRoSWNvbihcclxuICAgICAgICAgICAgICAgIHRyYWluaW5nLnZlc3NlbD8uaWQsXHJcbiAgICAgICAgICAgICAgICB0cmFpbmluZy52ZXNzZWwsXHJcbiAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICBpZDogdHJhaW5pbmcuaWQsXHJcbiAgICAgICAgICAgICAgICBkdWVEYXRlOiB0cmFpbmluZy5kYXRlLCAvLyBNYXAgZGF0ZSB0byBkdWVEYXRlIGZvciBjb25zaXN0ZW5jeVxyXG4gICAgICAgICAgICAgICAgdmVzc2VsSUQ6IHRyYWluaW5nLnZlc3NlbD8uaWQsXHJcbiAgICAgICAgICAgICAgICB2ZXNzZWw6IGNvbXBsZXRlVmVzc2VsRGF0YSxcclxuICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZUlEOiB0cmFpbmluZy50cmFpbmluZ1R5cGVzPy5ub2Rlcz8uWzBdPy5pZCxcclxuICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZTogdHJhaW5pbmcudHJhaW5pbmdUeXBlcz8ubm9kZXM/LlswXSB8fCB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICcnLFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIG1lbWJlcnM6IHRyYWluaW5nLm1lbWJlcnM/Lm5vZGVzIHx8IFtdLFxyXG4gICAgICAgICAgICAgICAgdHJhaW5lcjogdHJhaW5pbmcudHJhaW5lciwgLy8gS2VlcCB0cmFpbmVyIGluZm8gZm9yIGNvbXBsZXRlZCB0cmFpbmluZ3NcclxuICAgICAgICAgICAgICAgIHN0YXR1czoge1xyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsOiAnQ29tcGxldGVkJyxcclxuICAgICAgICAgICAgICAgICAgICBpc092ZXJkdWU6IGZhbHNlLFxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzOiAnYm9yZGVyIHJvdW5kZWQgYm9yZGVyLWJvcmRlciB0ZXh0LWlucHV0IGJnLW91dGVyLXNwYWNlLTUwIHAtMiBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXInLFxyXG4gICAgICAgICAgICAgICAgICAgIGR1ZVdpdGhpblNldmVuRGF5czogZmFsc2UsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdMb2NhdGlvblR5cGU6IHRyYWluaW5nLnRyYWluaW5nTG9jYXRpb25UeXBlLFxyXG4gICAgICAgICAgICAgICAgLy8gQWRkIHByaW9yaXR5IGZvciBzb3J0aW5nIChoaWdoZXIgbnVtYmVyID0gaGlnaGVyIHByaW9yaXR5KVxyXG4gICAgICAgICAgICAgICAgcHJpb3JpdHk6IDAsIC8vIENvbXBsZXRlZCB0cmFpbmluZ3MgaGF2ZSBsb3dlc3QgcHJpb3JpdHlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ3JlYXRlIHVuaWZpZWQgYW5kIHNvcnRlZCB0cmFpbmluZyBkYXRhXHJcbiAgICBjb25zdCBjcmVhdGVVbmlmaWVkVHJhaW5pbmdEYXRhID0gKCkgPT4ge1xyXG4gICAgICAgIC8vIFRyYW5zZm9ybSBjb21wbGV0ZWQgdHJhaW5pbmdzXHJcbiAgICAgICAgY29uc3QgdHJhbnNmb3JtZWRDb21wbGV0ZWQgPSB0cmFuc2Zvcm1Db21wbGV0ZWRUcmFpbmluZ3MoXHJcbiAgICAgICAgICAgIHRyYWluaW5nTGlzdCB8fCBbXSxcclxuICAgICAgICApXHJcblxyXG4gICAgICAgIC8vIEFkZCBwcmlvcml0eSB0byBvdmVyZHVlL3VwY29taW5nIHRyYWluaW5nc1xyXG4gICAgICAgIGNvbnN0IHByaW9yaXRpemVkRHVlcyA9ICh0cmFpbmluZ1Nlc3Npb25EdWVzIHx8IFtdKS5tYXAoKGR1ZTogYW55KSA9PiAoe1xyXG4gICAgICAgICAgICAuLi5kdWUsXHJcbiAgICAgICAgICAgIHByaW9yaXR5OiBkdWUuc3RhdHVzPy5pc092ZXJkdWVcclxuICAgICAgICAgICAgICAgID8gM1xyXG4gICAgICAgICAgICAgICAgOiBkdWUuc3RhdHVzPy5kdWVXaXRoaW5TZXZlbkRheXNcclxuICAgICAgICAgICAgICAgICAgPyAyXHJcbiAgICAgICAgICAgICAgICAgIDogMSxcclxuICAgICAgICB9KSlcclxuXHJcbiAgICAgICAgLy8gQ29tYmluZSBhbGwgdHJhaW5pbmcgZGF0YVxyXG4gICAgICAgIGNvbnN0IGFsbFRyYWluaW5ncyA9IFsuLi5wcmlvcml0aXplZER1ZXMsIC4uLnRyYW5zZm9ybWVkQ29tcGxldGVkXVxyXG5cclxuICAgICAgICAvLyBTb3J0IGJ5IHByaW9yaXR5IChkZXNjZW5kaW5nKSwgdGhlbiBieSBkdWUgZGF0ZSAoYXNjZW5kaW5nIGZvciBvdmVyZHVlL3VwY29taW5nLCBkZXNjZW5kaW5nIGZvciBjb21wbGV0ZWQpXHJcbiAgICAgICAgcmV0dXJuIGFsbFRyYWluaW5ncy5zb3J0KChhOiBhbnksIGI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAvLyBGaXJzdCBzb3J0IGJ5IHByaW9yaXR5IChvdmVyZHVlID4gdXBjb21pbmcgPiBvdGhlciA+IGNvbXBsZXRlZClcclxuICAgICAgICAgICAgaWYgKGEucHJpb3JpdHkgIT09IGIucHJpb3JpdHkpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBiLnByaW9yaXR5IC0gYS5wcmlvcml0eVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBXaXRoaW4gc2FtZSBwcmlvcml0eSwgc29ydCBieSBkYXRlXHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGVBID0gbmV3IERhdGUoYS5kdWVEYXRlIHx8IDApLmdldFRpbWUoKVxyXG4gICAgICAgICAgICBjb25zdCBkYXRlQiA9IG5ldyBEYXRlKGIuZHVlRGF0ZSB8fCAwKS5nZXRUaW1lKClcclxuXHJcbiAgICAgICAgICAgIC8vIEZvciBvdmVyZHVlIGFuZCB1cGNvbWluZyAocHJpb3JpdHkgPiAwKSwgc29ydCBieSBkdWUgZGF0ZSBhc2NlbmRpbmcgKGVhcmxpZXN0IGZpcnN0KVxyXG4gICAgICAgICAgICBpZiAoYS5wcmlvcml0eSA+IDApIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBkYXRlQSAtIGRhdGVCXHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIEZvciBjb21wbGV0ZWQgdHJhaW5pbmdzIChwcmlvcml0eSA9IDApLCBzb3J0IGJ5IGRhdGUgZGVzY2VuZGluZyAobW9zdCByZWNlbnQgZmlyc3QpXHJcbiAgICAgICAgICAgIHJldHVybiBkYXRlQiAtIGRhdGVBXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICAvLyBSb3cgc3RhdHVzIGV2YWx1YXRpb24gZm9yIEZpbHRlcmVkVGFibGUgc3R5bGluZ1xyXG4gICAgY29uc3QgZ2V0VHJhaW5pbmdSb3dTdGF0dXMgPSAoXHJcbiAgICAgICAgdHJhaW5pbmc6IGFueSxcclxuICAgICk6ICdvdmVyZHVlJyB8ICd1cGNvbWluZycgfCAnbm9ybWFsJyA9PiB7XHJcbiAgICAgICAgaWYgKCF0cmFpbmluZy5zdGF0dXMpIHJldHVybiAnbm9ybWFsJ1xyXG5cclxuICAgICAgICBpZiAodHJhaW5pbmcuc3RhdHVzLmlzT3ZlcmR1ZSkge1xyXG4gICAgICAgICAgICByZXR1cm4gJ292ZXJkdWUnXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAodHJhaW5pbmcuc3RhdHVzLmR1ZVdpdGhpblNldmVuRGF5cykge1xyXG4gICAgICAgICAgICByZXR1cm4gJ3VwY29taW5nJ1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuICdub3JtYWwnXHJcbiAgICB9XHJcblxyXG4gICAgLy8gVW5pZmllZCBjb2x1bW4gZGVmaW5pdGlvbnMgZm9yIHRoZSB0cmFpbmluZyB0YWJsZVxyXG4gICAgY29uc3QgY3JlYXRlVW5pZmllZFRyYWluaW5nQ29sdW1ucyA9ICgpID0+XHJcbiAgICAgICAgY3JlYXRlQ29sdW1ucyhbXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndGl0bGUnLFxyXG4gICAgICAgICAgICAgICAgaGVhZGVyOiAnVHJhaW5pbmcnLFxyXG4gICAgICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRyYWluaW5nOiBhbnkgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TW9iaWxlVHJhaW5pbmdDYXJkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhPXt0cmFpbmluZ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklkPXttZW1iZXJJZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnN0YXR1cz8ubGFiZWwgPT09ICdDb21wbGV0ZWQnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2NvbXBsZXRlZCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnb3ZlcmR1ZSdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBTb3J0IGJ5IHByaW9yaXR5IGZpcnN0LCB0aGVuIGJ5IHRyYWluaW5nIHR5cGUgbmFtZVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHByaW9yaXR5QSA9IHJvd0E/Lm9yaWdpbmFsPy5wcmlvcml0eSB8fCAwXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJpb3JpdHlCID0gcm93Qj8ub3JpZ2luYWw/LnByaW9yaXR5IHx8IDBcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHByaW9yaXR5QSAhPT0gcHJpb3JpdHlCKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBwcmlvcml0eUIgLSBwcmlvcml0eUFcclxuICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5hbWVBID0gcm93QT8ub3JpZ2luYWw/LnRyYWluaW5nVHlwZT8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBuYW1lQiA9IHJvd0I/Lm9yaWdpbmFsPy50cmFpbmluZ1R5cGU/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5hbWVBLmxvY2FsZUNvbXBhcmUobmFtZUIpXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBhY2Nlc3NvcktleTogJ3RyYWluaW5nVHlwZScsXHJcbiAgICAgICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnbGVmdCcsXHJcbiAgICAgICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2x1bW49e2NvbHVtbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJUcmFpbmluZyBUeXBlXCJcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICd0YWJsZXQtbWQnLFxyXG4gICAgICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRyYWluaW5nID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdHJhaW5pbmdUeXBlID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcudHJhaW5pbmdUeXBlPy50aXRsZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZy50cmFpbmluZ1R5cGVzPy5ub2Rlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPy5tYXAoKHQ6IGFueSkgPT4gdC50aXRsZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5qb2luKCcsICcpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICcnXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtc20gZm9udC1tZWRpdW0nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnN0YXR1cz8uaXNPdmVyZHVlICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LWNpbm5hYmFyLTUwMCBob3Zlcjp0ZXh0LWNpbm5hYmFyLTcwMCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcuc3RhdHVzPy5kdWVXaXRoaW5TZXZlbkRheXMgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtZmlyZS1idXNoLTYwMCBob3Zlcjp0ZXh0LWZpcmUtYnVzaC03MDAnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dHJhaW5pbmdUeXBlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPSByb3dBPy5vcmlnaW5hbD8udHJhaW5pbmdUeXBlPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy50cmFpbmluZ1R5cGU/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndmVzc2VsJyxcclxuICAgICAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyxcclxuICAgICAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIlZlc3NlbFwiIC8+XHJcbiAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgYnJlYWtwb2ludDogJ2xhbmRzY2FwZScsXHJcbiAgICAgICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdHJhaW5pbmcgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbm93cmFwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3RyYWluaW5nLnZlc3NlbD8udGl0bGUgfHwgJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TG9jYXRpb25Nb2RhbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbD17dHJhaW5pbmcudmVzc2VsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb25DbGFzc05hbWU9XCJzaXplLThcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/LnZlc3NlbD8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSByb3dCPy5vcmlnaW5hbD8udmVzc2VsPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZUEubG9jYWxlQ29tcGFyZSh2YWx1ZUIpXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBhY2Nlc3NvcktleTogJ2NyZXcnLFxyXG4gICAgICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJDcmV3XCIgLz5cclxuICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICBicmVha3BvaW50OiAnbGFwdG9wJyxcclxuICAgICAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB0cmFpbmluZyA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG1lbWJlcnMgPSB0cmFpbmluZy5tZW1iZXJzIHx8IFtdXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMSBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlcnMuc2xpY2UoMCwgNSkubWFwKChtZW1iZXI6IGFueSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGtleT17bWVtYmVyLmlkfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnN0YXR1cz8uaXNPdmVyZHVlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdkZXN0cnVjdGl2ZSdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdHJhaW5pbmcuc3RhdHVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8uZHVlV2l0aGluU2V2ZW5EYXlzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3dhcm5pbmcnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3NlY29uZGFyeSdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXJGYWxsYmFjayBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDcmV3SW5pdGlhbHMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLnN1cm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXBUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWVtYmVyLmZpcnN0TmFtZX17JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlci5zdXJuYW1lID8/ICcnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXBDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlcnMubGVuZ3RoID4gNSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtbC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICt7bWVtYmVycy5sZW5ndGggLSA1fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgYCR7cm93QT8ub3JpZ2luYWw/Lm1lbWJlcnM/LlswXT8uZmlyc3ROYW1lID8/ICcnfSAke3Jvd0E/Lm9yaWdpbmFsPy5tZW1iZXJzPy5bMF0/LnN1cm5hbWUgPz8gJyd9YCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGAke3Jvd0I/Lm9yaWdpbmFsPy5tZW1iZXJzPy5bMF0/LmZpcnN0TmFtZSA/PyAnJ30gJHtyb3dCPy5vcmlnaW5hbD8ubWVtYmVycz8uWzBdPy5zdXJuYW1lID8/ICcnfWAgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgJydcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdzdGF0dXMnLFxyXG4gICAgICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJTdGF0dXNcIiAvPlxyXG4gICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICdsYW5kc2NhcGUnLFxyXG4gICAgICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRyYWluaW5nID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAncHgtMiBweS0xIHJvdW5kZWQtbWQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWNlbnRlcicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcuc3RhdHVzPy5pc092ZXJkdWUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLWRlc3RydWN0aXZlLzEwIHRleHQtZGVzdHJ1Y3RpdmUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnN0YXR1cz8uZHVlV2l0aGluU2V2ZW5EYXlzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdiZy13YXJuaW5nLzEwIHRleHQtd2FybmluZycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcuc3RhdHVzPy5sYWJlbCA9PT0gJ0NvbXBsZXRlZCcgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLW11dGVkIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0cmFpbmluZy5zdGF0dXM/LmxhYmVsIHx8ICdVbmtub3duJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJpb3JpdHlBID0gcm93QT8ub3JpZ2luYWw/LnByaW9yaXR5IHx8IDBcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcmlvcml0eUIgPSByb3dCPy5vcmlnaW5hbD8ucHJpb3JpdHkgfHwgMFxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBwcmlvcml0eUIgLSBwcmlvcml0eUFcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnZHVlRGF0ZScsXHJcbiAgICAgICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAncmlnaHQnLFxyXG4gICAgICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiRGF0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgYnJlYWtwb2ludDogJ3RhYmxldC1tZCcsXHJcbiAgICAgICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdHJhaW5pbmcgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRlVGV4dCA9IGZvcm1hdERhdGUodHJhaW5pbmcuZHVlRGF0ZSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXJpZ2h0JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZy5zdGF0dXM/LmlzT3ZlcmR1ZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1jaW5uYWJhci01MDAgaG92ZXI6dGV4dC1jaW5uYWJhci03MDAnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnN0YXR1cz8uZHVlV2l0aGluU2V2ZW5EYXlzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LWZpcmUtYnVzaC02MDAgaG92ZXI6dGV4dC1maXJlLWJ1c2gtNzAwJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2RhdGVUZXh0fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRlQSA9IG5ldyBEYXRlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICByb3dBPy5vcmlnaW5hbD8uZHVlRGF0ZSB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgICkuZ2V0VGltZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0ZUIgPSBuZXcgRGF0ZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgcm93Qj8ub3JpZ2luYWw/LmR1ZURhdGUgfHwgMCxcclxuICAgICAgICAgICAgICAgICAgICApLmdldFRpbWUoKVxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBkYXRlQiAtIGRhdGVBXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIF0pXHJcblxyXG4gICAgLy8gR2V0IHVuaWZpZWQgdHJhaW5pbmcgZGF0YSBiYXNlZCBvbiBmaWx0ZXIgc2V0dGluZ3NcclxuICAgIGNvbnN0IGdldFVuaWZpZWRUcmFpbmluZ0RhdGEgPSAoKSA9PiB7XHJcbiAgICAgICAgaWYgKGV4Y2x1ZGVGaWx0ZXJzLmluY2x1ZGVzKCdvdmVyZHVlVG9nZ2xlJykpIHtcclxuICAgICAgICAgICAgLy8gVXNlIHRoZSBuZXcgdW5pZmllZCBhbmQgc29ydGVkIGRhdGEgd2hlbiBvdmVyZHVlVG9nZ2xlIGlzIGV4Y2x1ZGVkXHJcbiAgICAgICAgICAgIGNvbnN0IHVuaWZpZWREYXRhID0gY3JlYXRlVW5pZmllZFRyYWluaW5nRGF0YSgpXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgICAgICAgICAgJ/Cfk4ogRmluYWwgdW5pZmllZCB0cmFpbmluZyBkYXRhIGZvciB0YWJsZTonLFxyXG4gICAgICAgICAgICAgICAgdW5pZmllZERhdGEsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgICAgcmV0dXJuIHVuaWZpZWREYXRhXHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIFJldHVybiBvbmx5IG92ZXJkdWUvdXBjb21pbmcgdHJhaW5pbmdzIHdoZW4gdG9nZ2xlIGlzIGF2YWlsYWJsZVxyXG4gICAgICAgIGNvbnN0IGR1ZXNEYXRhID0gdHJhaW5pbmdTZXNzaW9uRHVlcyB8fCBbXVxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIEZpbmFsIHRyYWluaW5nIGR1ZXMgZGF0YSBmb3IgdGFibGU6JywgZHVlc0RhdGEpXHJcbiAgICAgICAgcmV0dXJuIGR1ZXNEYXRhXHJcbiAgICB9XHJcblxyXG4gICAgaWYgKFxyXG4gICAgICAgICFwZXJtaXNzaW9ucyB8fFxyXG4gICAgICAgICghaGFzUGVybWlzc2lvbignRURJVF9UUkFJTklORycsIHBlcm1pc3Npb25zKSAmJlxyXG4gICAgICAgICAgICAhaGFzUGVybWlzc2lvbignVklFV19UUkFJTklORycsIHBlcm1pc3Npb25zKSAmJlxyXG4gICAgICAgICAgICAhaGFzUGVybWlzc2lvbignUkVDT1JEX1RSQUlOSU5HJywgcGVybWlzc2lvbnMpICYmXHJcbiAgICAgICAgICAgICFoYXNQZXJtaXNzaW9uKCdWSUVXX01FTUJFUl9UUkFJTklORycsIHBlcm1pc3Npb25zKSlcclxuICAgICkge1xyXG4gICAgICAgIHJldHVybiAhcGVybWlzc2lvbnMgPyAoXHJcbiAgICAgICAgICAgIDxMb2FkaW5nIC8+XHJcbiAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgPExvYWRpbmcgZXJyb3JNZXNzYWdlPVwiT29wcyBZb3UgZG8gbm90IGhhdmUgdGhlIHBlcm1pc3Npb24gdG8gdmlldyB0aGlzIHNlY3Rpb24uXCIgLz5cclxuICAgICAgICApXHJcbiAgICB9XHJcblxyXG4gICAgLy8gR2V0IHRoZSB1bmlmaWVkIHRyYWluaW5nIGRhdGFcclxuICAgIGNvbnN0IHVuaWZpZWRUcmFpbmluZ0RhdGEgPSBnZXRVbmlmaWVkVHJhaW5pbmdEYXRhKClcclxuICAgIGNvbnN0IGlzRGF0YUxvYWRpbmcgPSB0cmFpbmluZ0xpc3RMb2FkaW5nIHx8IHRyYWluaW5nU2Vzc2lvbkR1ZXNMb2FkaW5nXHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTVcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUcmFpbmluZ0xpc3RGaWx0ZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySWQ9e21lbWJlcklkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlRmlsdGVyQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvdmVyZHVlU3dpdGNoZXI9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBleGNsdWRlRmlsdGVycz17ZXhjbHVkZUZpbHRlcnN9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7aXNEYXRhTG9hZGluZyA/IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC04IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNCBhbmltYXRlLXNwaW5cIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIExvYWRpbmcgdHJhaW5pbmcgZGF0YS4uLlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICkgOiB1bmlmaWVkVHJhaW5pbmdEYXRhPy5sZW5ndGggPiAwID8gKFxyXG4gICAgICAgICAgICAgICAgPEZpbHRlcmVkVGFibGVcclxuICAgICAgICAgICAgICAgICAgICBjb2x1bW5zPXtjcmVhdGVVbmlmaWVkVHJhaW5pbmdDb2x1bW5zKCl9XHJcbiAgICAgICAgICAgICAgICAgICAgZGF0YT17dW5pZmllZFRyYWluaW5nRGF0YX1cclxuICAgICAgICAgICAgICAgICAgICByb3dTdGF0dXM9e2dldFRyYWluaW5nUm93U3RhdHVzfVxyXG4gICAgICAgICAgICAgICAgICAgIHBhZ2VTaXplPXsyMH1cclxuICAgICAgICAgICAgICAgICAgICBzaG93VG9vbGJhcj17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncm91cCBib3JkZXItYlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGNvbC1zcGFuLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgZ2FwLTIgcC0yIHB0LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFdPVyEgTG9vayBhdCB0aGF0LiBBbGwgeW91ciBjcmV3IGFyZSBzaGlwLXNoYXBlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFuZCB0cmFpbmVkIHRvIHRoZSBnaWxscy4gR3JlYXQgam9iLCBjYXB0YWluIVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDcmV3VHJhaW5pbmdMaXN0XHJcblxyXG5leHBvcnQgY29uc3QgT3ZlcmR1ZVRyYWluaW5nTGlzdCA9ICh7XHJcbiAgICB0cmFpbmluZ1Nlc3Npb25EdWVzLFxyXG4gICAgaXNWZXNzZWxWaWV3ID0gZmFsc2UsXHJcbiAgICBoaWRlQ3Jld0NvbHVtbiA9IGZhbHNlLFxyXG4gICAgcGFnZVNpemUgPSAyMCxcclxufTogYW55KSA9PiB7XHJcbiAgICBjb25zdCBpc1dpZGUgPSB1c2VNZWRpYVF1ZXJ5KCcobWluLXdpZHRoOiA3MjBweCknKVxyXG5cclxuICAgIGNvbnN0IGFsbENvbHVtbnMgPSBjcmVhdGVDb2x1bW5zKFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndGl0bGUnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdUcmFpbmluZycsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGE6IGFueSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIDxNb2JpbGVUcmFpbmluZ0NhcmQgZGF0YT17ZGF0YX0gdHlwZT1cIm92ZXJkdWVcIiAvPlxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3Zlc3NlbCcsXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyxcclxuICAgICAgICAgICAgaGVhZGVyOiAnVmVzc2VsJyxcclxuICAgICAgICAgICAgYnJlYWtwb2ludDogJ2xhbmRzY2FwZScsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGR1ZSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aXNWZXNzZWxWaWV3ID09IGZhbHNlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOnRhYmxlLWNlbGwgcC0yIGFsaWduLXRvcCBsZzphbGlnbi1taWRkbGUgaXRlbXMtY2VudGVyIHRleHQtbGVmdFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkdWUudmVzc2VsPy50aXRsZSB8fCAnJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/LnZlc3NlbD8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy52ZXNzZWw/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ2NyZXcnLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAncmlnaHQnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdDcmV3JyxcclxuICAgICAgICAgICAgYnJlYWtwb2ludDogJ2xhcHRvcCcsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGR1ZSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgY29uc3QgbWVtYmVycyA9IGR1ZS5tZW1iZXJzIHx8IFtdXHJcblxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGlzV2lkZSA/IChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlcnMubWFwKChtZW1iZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBrZXk9e21lbWJlci5pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdWUuc3RhdHVzLmlzT3ZlcmR1ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnZGVzdHJ1Y3RpdmUnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdzZWNvbmRhcnknXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q3Jld0luaXRpYWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLmZpcnN0TmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlci5maXJzdE5hbWV9eycgJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW1iZXIuc3VybmFtZSA/PyAnJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAnIXJvdW5kZWQtZnVsbCBzaXplLTEwJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1ZS5zdGF0dXM/LmNsYXNzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlcnMubGVuZ3RofVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID1cclxuICAgICAgICAgICAgICAgICAgICBgJHtyb3dBPy5vcmlnaW5hbD8ubWVtYmVycz8ubm9kZXM/LlswXT8uZmlyc3ROYW1lID8/ICcnfSAke3Jvd0E/Lm9yaWdpbmFsPy5tZW1iZXJzPy5ub2Rlcz8uWzBdPy5zdXJuYW1lID8/ICcnfWAgfHxcclxuICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID1cclxuICAgICAgICAgICAgICAgICAgICBgJHtyb3dCPy5vcmlnaW5hbD8ubWVtYmVycz8ubm9kZXM/LlswXT8uZmlyc3ROYW1lID8/ICcnfSAke3Jvd0I/Lm9yaWdpbmFsPy5tZW1iZXJzPy5ub2Rlcz8uWzBdPy5zdXJuYW1lID8/ICcnfWAgfHxcclxuICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdzdGF0dXMnLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAncmlnaHQnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdTdGF0dXMnLFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAnbGFuZHNjYXBlJyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZHVlID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtkdWUuc3RhdHVzPy5pc092ZXJkdWUgPyBkdWUuc3RhdHVzPy5jbGFzcyA6ICcnfSByb3VuZGVkLW1kIHctZml0ICFwLTIgdGV4dC1ub3dyYXBgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2R1ZS5zdGF0dXM/LmxhYmVsIHx8ICdVbmtub3duIFN0YXR1cyd9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgIF0pXHJcblxyXG4gICAgLy8gRmlsdGVyIG91dCBjcmV3IGNvbHVtbiB3aGVuIGhpZGVDcmV3Q29sdW1uIGlzIHRydWVcclxuICAgIGNvbnN0IGNvbHVtbnMgPSBoaWRlQ3Jld0NvbHVtblxyXG4gICAgICAgID8gYWxsQ29sdW1ucy5maWx0ZXIoKGNvbDogYW55KSA9PiBjb2wuYWNjZXNzb3JLZXkgIT09ICdjcmV3JylcclxuICAgICAgICA6IGFsbENvbHVtbnNcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDw+XHJcbiAgICAgICAgICAgIHt0cmFpbmluZ1Nlc3Npb25EdWVzPy5sZW5ndGggPiAwID8gKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgICAgICAgICAgICAgZGF0YT17dHJhaW5pbmdTZXNzaW9uRHVlc31cclxuICAgICAgICAgICAgICAgICAgICBwYWdlU2l6ZT17cGFnZVNpemV9XHJcbiAgICAgICAgICAgICAgICAgICAgc2hvd1Rvb2xiYXI9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZ3JvdXAgYm9yZGVyLWIgaG92ZXI6IGB9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGNvbC1zcGFuLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgZ2FwLTIgcC0yIHB0LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIiAgXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgV09XISBMb29rIGF0IHRoYXQuIEFsbCB5b3VyIGNyZXcgYXJlIHNoaXAtc2hhcGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYW5kIHRyYWluZWQgdG8gdGhlIGdpbGxzLiBHcmVhdCBqb2IsIGNhcHRhaW4hXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgPC8+XHJcbiAgICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIlRSQUlOSU5HX1NFU1NJT05TIiwiUkVBRF9UUkFJTklOR19TRVNTSU9OX0RVRVMiLCJ1c2VMYXp5UXVlcnkiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIlRyYWluaW5nTGlzdEZpbHRlciIsIkdldFRyYWluaW5nU2Vzc2lvblN0YXR1cyIsImZvcm1hdCIsImdldFBlcm1pc3Npb25zIiwiaGFzUGVybWlzc2lvbiIsImZvcm1hdERhdGUiLCJkYXRlU3RyaW5nIiwiZGF0ZSIsIkRhdGUiLCJMb2FkaW5nIiwiY3JlYXRlQ29sdW1ucyIsIkRhdGFUYWJsZSIsIkZpbHRlcmVkVGFibGUiLCJEYXRhVGFibGVTb3J0SGVhZGVyIiwiTG9hZGVyMiIsIkF2YXRhciIsIkF2YXRhckZhbGxiYWNrIiwiQ2FyZCIsImdldENyZXdJbml0aWFscyIsIlRvb2x0aXAiLCJUb29sdGlwQ29udGVudCIsIlRvb2x0aXBUcmlnZ2VyIiwidXNlTWVkaWFRdWVyeSIsImNuIiwidXNlVHJhaW5pbmdGaWx0ZXJzIiwiTG9jYXRpb25Nb2RhbCIsInVzZVZlc3NlbEljb25EYXRhIiwiTW9iaWxlVHJhaW5pbmdDYXJkIiwiQ3Jld1RyYWluaW5nTGlzdCIsIm1lbWJlcklkIiwidmVzc2VsSWQiLCJhcHBseUZpbHRlclJlZiIsImV4Y2x1ZGVGaWx0ZXJzIiwibGltaXQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ0cmFpbmluZ0xpc3QiLCJzZXRUcmFpbmluZ0xpc3QiLCJ0cmFpbmluZ1Nlc3Npb25EdWVzIiwic2V0VHJhaW5pbmdTZXNzaW9uRHVlcyIsImlzV2lkZSIsImdldFZlc3NlbFdpdGhJY29uIiwibG9hZGluZyIsInZlc3NlbERhdGFMb2FkaW5nIiwicXVlcnlUcmFpbmluZ0xpc3QiLCJ0cmFpbmluZ0xpc3RMb2FkaW5nIiwiZmV0Y2hQb2xpY3kiLCJvbkNvbXBsZXRlZCIsInJlc3BvbnNlIiwiY29uc29sZSIsImxvZyIsInJlYWRUcmFpbmluZ1Nlc3Npb25zIiwiZGF0YSIsIm5vZGVzIiwidHJhbnNmb3JtZWREYXRhIiwibWFwIiwiaXRlbSIsImNvbXBsZXRlVmVzc2VsRGF0YSIsInZlc3NlbCIsImlkIiwib25FcnJvciIsImVycm9yIiwibG9hZFRyYWluaW5nTGlzdCIsInN0YXJ0UGFnZSIsInNlYXJjaEZpbHRlciIsImZpbHRlciIsInZhcmlhYmxlcyIsIm9mZnNldCIsImxvYWRUcmFpbmluZ1Nlc3Npb25EdWVzIiwiZHVlRmlsdGVyIiwibWVtYmVySUQiLCJlcSIsInZlc3NlbElEIiwidHJhaW5pbmdUeXBlcyIsInRyYWluaW5nVHlwZUlEIiwiY29udGFpbnMiLCJtZW1iZXJzIiwiZHVlRGF0ZSIsIm5lIiwicmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMiLCJzZXRGaWx0ZXIiLCJoYW5kbGVGaWx0ZXJDaGFuZ2UiLCJpbml0aWFsRmlsdGVyIiwibG9hZExpc3QiLCJsb2FkRHVlcyIsInRvZ2dsZU92ZXJkdWUiLCJjdXJyZW50IiwiYXBwbHkiLCJvdmVyZHVlIiwic2V0T3ZlcmR1ZSIsInRyYWluaW5nU2Vzc2lvbkR1ZXNMb2FkaW5nIiwiZmlsdGVyZWREYXRhIiwic2VhTG9nc01lbWJlcnMiLCJzb21lIiwibSIsImR1ZVdpdGhTdGF0dXMiLCJkdWUiLCJzdGF0dXMiLCJncm91cGVkRHVlcyIsInJlZHVjZSIsImFjYyIsImtleSIsInRyYWluaW5nVHlwZSIsInRyYWluaW5nTG9jYXRpb25UeXBlIiwidHJhaW5pbmdTZXNzaW9uIiwicHVzaCIsIm1lbWJlciIsIm1lcmdlZER1ZXMiLCJPYmplY3QiLCJ2YWx1ZXMiLCJncm91cCIsIm1lcmdlZE1lbWJlcnMiLCJleGlzdGluZ01lbWJlciIsImZpbmQiLCJmaXJzdE5hbWUiLCJzdXJuYW1lIiwiZiIsInBlcm1pc3Npb25zIiwic2V0UGVybWlzc2lvbnMiLCJ0cmFuc2Zvcm1Db21wbGV0ZWRUcmFpbmluZ3MiLCJ0cmFpbmluZyIsInRpdGxlIiwidHJhaW5lciIsImxhYmVsIiwiaXNPdmVyZHVlIiwiY2xhc3MiLCJkdWVXaXRoaW5TZXZlbkRheXMiLCJwcmlvcml0eSIsImNyZWF0ZVVuaWZpZWRUcmFpbmluZ0RhdGEiLCJ0cmFuc2Zvcm1lZENvbXBsZXRlZCIsInByaW9yaXRpemVkRHVlcyIsImFsbFRyYWluaW5ncyIsInNvcnQiLCJhIiwiYiIsImRhdGVBIiwiZ2V0VGltZSIsImRhdGVCIiwiZ2V0VHJhaW5pbmdSb3dTdGF0dXMiLCJjcmVhdGVVbmlmaWVkVHJhaW5pbmdDb2x1bW5zIiwiYWNjZXNzb3JLZXkiLCJoZWFkZXIiLCJjZWxsIiwicm93Iiwib3JpZ2luYWwiLCJ0eXBlIiwic29ydGluZ0ZuIiwicm93QSIsInJvd0IiLCJwcmlvcml0eUEiLCJwcmlvcml0eUIiLCJuYW1lQSIsIm5hbWVCIiwibG9jYWxlQ29tcGFyZSIsImNlbGxBbGlnbm1lbnQiLCJjb2x1bW4iLCJicmVha3BvaW50IiwidCIsImpvaW4iLCJkaXYiLCJjbGFzc05hbWUiLCJ2YWx1ZUEiLCJ2YWx1ZUIiLCJzcGFuIiwiaWNvbkNsYXNzTmFtZSIsInNsaWNlIiwic2l6ZSIsInZhcmlhbnQiLCJsZW5ndGgiLCJkYXRlVGV4dCIsImdldFVuaWZpZWRUcmFpbmluZ0RhdGEiLCJpbmNsdWRlcyIsInVuaWZpZWREYXRhIiwiZHVlc0RhdGEiLCJlcnJvck1lc3NhZ2UiLCJ1bmlmaWVkVHJhaW5pbmdEYXRhIiwiaXNEYXRhTG9hZGluZyIsIm9uQ2hhbmdlIiwib3ZlcmR1ZVN3aXRjaGVyIiwiY29sdW1ucyIsInJvd1N0YXR1cyIsInBhZ2VTaXplIiwic2hvd1Rvb2xiYXIiLCJwIiwiT3ZlcmR1ZVRyYWluaW5nTGlzdCIsImlzVmVzc2VsVmlldyIsImhpZGVDcmV3Q29sdW1uIiwiYWxsQ29sdW1ucyIsImNvbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});