'use client'

import {
    TRAINING_SESSIONS,
    READ_TRAINING_SESSION_DUES,
} from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import { useCallback, useEffect, useState } from 'react'
import Link from 'next/link'
import { TrainingListFilter } from '@/components/filter'
import { GetTrainingSessionStatus } from '@/app/lib/actions'
import { format } from 'date-fns'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'

// Helper function to format dates using date-fns
const formatDate = (dateString: any) => {
    if (!dateString) return ''
    try {
        const date = new Date(dateString)
        return format(date, 'dd/MM/yy')
    } catch {
        return ''
    }
}
import Loading from '@/app/loading'

import {
    createColumns,
    DataTable,
    ExtendedColumnDef,
    FilteredTable,
} from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { Loader2 } from 'lucide-react'
import {
    Avatar,
    AvatarFallback,
    Card,
    getCrewInitials,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { useMediaQuery } from '@reactuses/core'
import { cn } from '@/app/lib/utils'
import { useTrainingFilters } from './hooks/useTrainingFilters'
import { useQueryState } from 'nuqs'
import { LocationModal } from '../vessels/list'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'

import { MobileTrainingCard } from './components/mobile-training-card'

const CrewTrainingList = ({
    memberId = 0,
    vesselId = 0,
    applyFilterRef,
    excludeFilters = [],
}: {
    memberId?: number
    vesselId?: number
    applyFilterRef?: any
    excludeFilters?: string[]
}) => {
    const limit = 100
    const [isLoading, setIsLoading] = useState(true)
    const [trainingList, setTrainingList] = useState([] as any)
    const [trainingSessionDues, setTrainingSessionDues] = useState([] as any)
    const [page, setPage] = useState(0)
    const [vesselIdOptions, setVesselIdOptions] = useState([] as any)
    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = useState(
        [] as any,
    )
    const [trainerIdOptions, setTrainerIdOptions] = useState([] as any)
    const [crewIdOptions, setCrewIdOptions] = useState([] as any)
    const [overdueSwitcher, setOverdueSwitcher] = useQueryState('overdue')
    const isWide = useMediaQuery('(min-width: 720px)')
    const { getVesselWithIcon, loading: vesselDataLoading } =
        useVesselIconData()

    // Create a boolean state wrapper for the useTrainingFilters hook
    const [overdueBoolean, setOverdueBoolean] = useState<boolean>(false)

    // Sync the boolean state with the query state
    useEffect(() => {
        setOverdueBoolean(overdueSwitcher === 'true')
    }, [overdueSwitcher])

    // Create a wrapper function that converts boolean to string for the query state
    const toggleOverdueWrapper = useCallback(
        (value: boolean | ((prev: boolean) => boolean)) => {
            if (typeof value === 'function') {
                setOverdueBoolean((prev) => {
                    const newValue = value(prev)
                    setOverdueSwitcher(newValue ? 'true' : 'false')
                    return newValue
                })
            } else {
                setOverdueBoolean(value)
                setOverdueSwitcher(value ? 'true' : 'false')
            }
        },
        [setOverdueSwitcher],
    )

    const [queryTrainingList, { loading: trainingListLoading }] = useLazyQuery(
        TRAINING_SESSIONS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessions.nodes

                // Transform vessel data to include complete vessel information with position
                const transformedData = data.map((item: any) => {
                    const completeVesselData = getVesselWithIcon(
                        item.vessel.id,
                        item.vessel,
                    )

                    return {
                        ...item,
                        vessel: completeVesselData,
                    }
                })

                const vesselIDs = Array.from(
                    new Set(data.map((item: any) => item.vessel.id)),
                ).filter((id: any) => +id !== 0)
                const trainingTypeIDs = Array.from(
                    new Set(
                        data.flatMap((item: any) =>
                            item.trainingTypes.nodes.map((t: any) => t.id),
                        ),
                    ),
                )
                const trainerIDs = Array.from(
                    new Set(data.map((item: any) => item.trainerID)),
                ).filter((id: any) => +id !== 0)
                const memberIDs = Array.from(
                    new Set(
                        data.flatMap((item: any) =>
                            item.members.nodes.map((t: any) => t.id),
                        ),
                    ),
                )

                if (transformedData) {
                    setTrainingList(transformedData)
                    setVesselIdOptions(vesselIDs)
                    setTrainingTypeIdOptions(trainingTypeIDs)
                    setTrainerIdOptions(trainerIDs)
                    setCrewIdOptions(memberIDs)
                }
                setPageInfo(response.readTrainingSessions.pageInfo)
            },
            onError: (error: any) => {
                console.error('queryTrainingList error', error)
            },
        },
    )

    const loadTrainingList = async (
        startPage: number = 0,
        searchFilter: any = { ...filter },
    ) => {
        await queryTrainingList({
            variables: {
                filter: searchFilter,
                offset: startPage * limit,
                limit: limit,
            },
        })
    }

    const loadTrainingSessionDues = async (filter: any) => {
        const dueFilter: any = {}
        if (memberId > 0) {
            dueFilter.memberID = { eq: +memberId }
        }
        if (vesselId > 0) {
            dueFilter.vesselID = { eq: +vesselId }
        }
        if (filter.vesselID) {
            dueFilter.vesselID = filter.vesselID
        }
        if (filter.trainingTypes) {
            dueFilter.trainingTypeID = { eq: filter.trainingTypes.id.contains }
        }
        if (filter.members) {
            dueFilter.memberID = { eq: filter.members.id.contains }
        }
        if (filter.date) {
            dueFilter.dueDate = filter.date
        } else {
            dueFilter.dueDate = { ne: null }
        }
        await readTrainingSessionDues({
            variables: {
                filter: dueFilter,
            },
        })
    }

    const handleNavigationClick = (newPage: any) => {
        if (newPage < 0 || newPage === page) return
        setPage(newPage)
        loadTrainingSessionDues(filter)
        loadTrainingList(newPage, filter)
    }

    const {
        filter,
        setFilter, // used in the startup effect
        handleFilterChange, // replaces the old handleFilterOnChange
    } = useTrainingFilters({
        initialFilter: {},
        loadList: loadTrainingList,
        loadDues: loadTrainingSessionDues,
        toggleOverdue: toggleOverdueWrapper,
    })

    useEffect(() => {
        if (applyFilterRef)
            applyFilterRef.current = {
                apply: handleFilterChange, // trigger it
                overdue: overdueBoolean, // read snapshot
                setOverdue: toggleOverdueWrapper, // toggle directly if you want
            }
    }, [handleFilterChange, overdueBoolean, toggleOverdueWrapper])

    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] =
        useLazyQuery(READ_TRAINING_SESSION_DUES, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessionDues.nodes
                if (data) {
                    // Filter out crew members who are no longer assigned to the vessel.
                    const filteredData = data.filter((item: any) =>
                        item.vessel.seaLogsMembers.nodes.some((m: any) => {
                            return m.id === item.memberID
                        }),
                    )
                    const dueWithStatus = filteredData.map((due: any) => {
                        return { ...due, status: GetTrainingSessionStatus(due) }
                    })
                    // Return only due within 7 days and overdue
                    // const filteredDueWithStatus = dueWithStatus.filter(
                    //     (item: any) => {
                    //         return (
                    //             item.status.isOverdue ||
                    //             (item.status.isOverdue === false &&
                    //                 item.status.dueWithinSevenDays === true)
                    //         )
                    //     },
                    // )
                    // const groupedDues = filteredDueWithStatus.reduce(
                    const groupedDues = dueWithStatus.reduce(
                        (acc: any, due: any) => {
                            const key = `${due.vesselID}-${due.trainingTypeID}-${due.dueDate}`
                            if (!acc[key]) {
                                acc[key] = {
                                    id: due.id,
                                    vesselID: due.vesselID,
                                    vessel: due.vessel,
                                    trainingTypeID: due.trainingTypeID,
                                    trainingType: due.trainingType,
                                    dueDate: due.dueDate,
                                    status: due.status,
                                    trainingLocationType:
                                        due.trainingSession
                                            .trainingLocationType,
                                    members: [],
                                }
                            }
                            acc[key].members.push(due.member)
                            return acc
                        },
                        {},
                    )

                    const mergedDues = Object.values(groupedDues).map(
                        (group: any) => {
                            const mergedMembers = group.members.reduce(
                                (acc: any, member: any) => {
                                    const existingMember = acc.find(
                                        (m: any) => m.id === member.id,
                                    )
                                    if (existingMember) {
                                        existingMember.firstName =
                                            member.firstName
                                        existingMember.surname = member.surname
                                    } else {
                                        acc.push(member)
                                    }
                                    return acc
                                },
                                [],
                            )
                            return {
                                id: group.id,
                                vesselID: group.vesselID,
                                vessel: group.vessel,
                                trainingTypeID: group.trainingTypeID,
                                trainingType: group.trainingType,
                                status: group.status,
                                dueDate: group.dueDate,
                                trainingLocationType:
                                    group.trainingLocationType,
                                members: mergedMembers,
                            }
                        },
                    )
                    setTrainingSessionDues(mergedDues)
                }
            },
            onError: (error: any) => {
                console.error('readTrainingSessionDues error', error)
            },
        })

    useEffect(() => {
        if (isLoading) {
            const f: { members?: any } = { ...filter }
            if (+memberId > 0) {
                f.members = { id: { contains: +memberId } }
            }
            setFilter(f)
            loadTrainingSessionDues(f)
            loadTrainingList(0, f)
            setIsLoading(false)
        }
    }, [isLoading])

    const [permissions, setPermissions] = useState<any>(false)

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    // Transform completed training sessions to match unified structure
    const transformCompletedTrainings = (trainingList: any[]) => {
        return trainingList.map((training: any) => {
            // Ensure vessel has complete data including position
            const completeVesselData = getVesselWithIcon(
                training.vessel?.id,
                training.vessel,
            )

            return {
                id: training.id,
                dueDate: training.date, // Map date to dueDate for consistency
                vesselID: training.vessel?.id,
                vessel: completeVesselData,
                trainingTypeID: training.trainingTypes?.nodes?.[0]?.id,
                trainingType: training.trainingTypes?.nodes?.[0] || {
                    title: '',
                },
                members: training.members?.nodes || [],
                trainer: training.trainer, // Keep trainer info for completed trainings
                status: {
                    label: 'Completed',
                    isOverdue: false,
                    class: 'border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center',
                    dueWithinSevenDays: false,
                },
                trainingLocationType: training.trainingLocationType,
                // Add priority for sorting (higher number = higher priority)
                priority: 0, // Completed trainings have lowest priority
            }
        })
    }

    // Create unified and sorted training data
    const createUnifiedTrainingData = () => {
        // Transform completed trainings
        const transformedCompleted = transformCompletedTrainings(
            trainingList || [],
        )

        // Add priority to overdue/upcoming trainings
        const prioritizedDues = (trainingSessionDues || []).map((due: any) => ({
            ...due,
            priority: due.status?.isOverdue
                ? 3
                : due.status?.dueWithinSevenDays
                  ? 2
                  : 1,
        }))

        // Combine all training data
        const allTrainings = [...prioritizedDues, ...transformedCompleted]

        // Sort by priority (descending), then by due date (ascending for overdue/upcoming, descending for completed)
        return allTrainings.sort((a: any, b: any) => {
            // First sort by priority (overdue > upcoming > other > completed)
            if (a.priority !== b.priority) {
                return b.priority - a.priority
            }

            // Within same priority, sort by date
            const dateA = new Date(a.dueDate || 0).getTime()
            const dateB = new Date(b.dueDate || 0).getTime()

            // For overdue and upcoming (priority > 0), sort by due date ascending (earliest first)
            if (a.priority > 0) {
                return dateA - dateB
            }

            // For completed trainings (priority = 0), sort by date descending (most recent first)
            return dateB - dateA
        })
    }

    // Combined loading state for unified view
    const isUnifiedDataLoading = excludeFilters.includes('overdueToggle')
        ? trainingListLoading || trainingSessionDuesLoading
        : false

    // Row status evaluation for FilteredTable styling
    const getTrainingRowStatus = (
        training: any,
    ): 'overdue' | 'upcoming' | 'normal' => {
        if (!training.status) return 'normal'

        if (training.status.isOverdue) {
            return 'overdue'
        }

        if (training.status.dueWithinSevenDays) {
            return 'upcoming'
        }

        return 'normal'
    }

    // Unified column definitions for the training table
    const createUnifiedTrainingColumns = () =>
        createColumns([
            {
                accessorKey: 'title',
                header: 'Training',
                cell: ({ row }: { row: any }) => {
                    const training: any = row.original
                    return (
                        <MobileTrainingCard
                            data={training}
                            memberId={memberId}
                            type={
                                training.status?.label === 'Completed'
                                    ? 'completed'
                                    : 'overdue'
                            }
                        />
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    // Sort by priority first, then by training type name
                    const priorityA = rowA?.original?.priority || 0
                    const priorityB = rowB?.original?.priority || 0

                    if (priorityA !== priorityB) {
                        return priorityB - priorityA
                    }

                    const nameA = rowA?.original?.trainingType?.title || ''
                    const nameB = rowB?.original?.trainingType?.title || ''
                    return nameA.localeCompare(nameB)
                },
            },
            {
                accessorKey: 'trainingType',
                cellAlignment: 'left',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader
                        column={column}
                        title="Training Type"
                    />
                ),
                breakpoint: 'tablet-md',
                cell: ({ row }: { row: any }) => {
                    const training = row.original
                    const trainingType =
                        training.trainingType?.title ||
                        training.trainingTypes?.nodes
                            ?.map((t: any) => t.title)
                            .join(', ') ||
                        ''

                    return (
                        <div
                            className={cn(
                                'text-sm font-medium',
                                training.status?.isOverdue &&
                                    'text-cinnabar-500 hover:text-cinnabar-700',
                                training.status?.dueWithinSevenDays &&
                                    'text-fire-bush-600 hover:text-fire-bush-700',
                            )}>
                            {trainingType}
                        </div>
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const valueA = rowA?.original?.trainingType?.title || ''
                    const valueB = rowB?.original?.trainingType?.title || ''
                    return valueA.localeCompare(valueB)
                },
            },
            {
                accessorKey: 'vessel',
                cellAlignment: 'left',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Vessel" />
                ),
                breakpoint: 'landscape',
                cell: ({ row }: { row: any }) => {
                    const training = row.original
                    return (
                        <div className="flex items-center gap-2">
                            <span className="text-sm text-nowrap">
                                {training.vessel?.title || ''}
                            </span>
                            <LocationModal
                                vessel={training.vessel}
                                iconClassName="size-8"
                            />
                        </div>
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const valueA = rowA?.original?.vessel?.title || ''
                    const valueB = rowB?.original?.vessel?.title || ''
                    return valueA.localeCompare(valueB)
                },
            },
            {
                accessorKey: 'crew',
                cellAlignment: 'center',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Crew" />
                ),
                breakpoint: 'laptop',
                cell: ({ row }: { row: any }) => {
                    const training = row.original
                    const members = training.members || []

                    return (
                        <div className="flex gap-1 justify-center">
                            {members.slice(0, 5).map((member: any) => (
                                <Tooltip key={member.id}>
                                    <TooltipTrigger>
                                        <Avatar
                                            size="sm"
                                            variant={
                                                training.status?.isOverdue
                                                    ? 'destructive'
                                                    : training.status
                                                            ?.dueWithinSevenDays
                                                      ? 'warning'
                                                      : 'secondary'
                                            }>
                                            <AvatarFallback className="text-sm">
                                                {getCrewInitials(
                                                    member.firstName,
                                                    member.surname,
                                                )}
                                            </AvatarFallback>
                                        </Avatar>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        {member.firstName}{' '}
                                        {member.surname ?? ''}
                                    </TooltipContent>
                                </Tooltip>
                            ))}
                            {members.length > 5 && (
                                <div className="text-xs text-muted-foreground ml-1">
                                    +{members.length - 5}
                                </div>
                            )}
                        </div>
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const valueA =
                        `${rowA?.original?.members?.[0]?.firstName ?? ''} ${rowA?.original?.members?.[0]?.surname ?? ''}` ||
                        ''
                    const valueB =
                        `${rowB?.original?.members?.[0]?.firstName ?? ''} ${rowB?.original?.members?.[0]?.surname ?? ''}` ||
                        ''
                    return valueA.localeCompare(valueB)
                },
            },
            {
                accessorKey: 'status',
                cellAlignment: 'center',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Status" />
                ),
                breakpoint: 'landscape',
                cell: ({ row }: { row: any }) => {
                    const training = row.original
                    return (
                        <div
                            className={cn(
                                'px-2 py-1 rounded-md text-xs font-medium text-center',
                                training.status?.isOverdue &&
                                    'bg-destructive/10 text-destructive',
                                training.status?.dueWithinSevenDays &&
                                    'bg-warning/10 text-warning',
                                training.status?.label === 'Completed' &&
                                    'bg-muted text-muted-foreground',
                            )}>
                            {training.status?.label || 'Unknown'}
                        </div>
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const priorityA = rowA?.original?.priority || 0
                    const priorityB = rowB?.original?.priority || 0
                    return priorityB - priorityA
                },
            },
            {
                accessorKey: 'dueDate',
                cellAlignment: 'right',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Date" />
                ),
                breakpoint: 'tablet-md',
                cell: ({ row }: { row: any }) => {
                    const training = row.original
                    const dateText = formatDate(training.dueDate)

                    return (
                        <div
                            className={cn(
                                'text-sm font-medium text-right',
                                training.status?.isOverdue &&
                                    'text-cinnabar-500 hover:text-cinnabar-700',
                                training.status?.dueWithinSevenDays &&
                                    'text-fire-bush-600 hover:text-fire-bush-700',
                            )}>
                            {dateText}
                        </div>
                    )
                },
                sortingFn: (rowA: any, rowB: any) => {
                    const dateA = new Date(
                        rowA?.original?.dueDate || 0,
                    ).getTime()
                    const dateB = new Date(
                        rowB?.original?.dueDate || 0,
                    ).getTime()
                    return dateB - dateA
                },
            },
        ])

    // Get unified training data based on filter settings
    const getUnifiedTrainingData = () => {
        if (excludeFilters.includes('overdueToggle')) {
            // Use the new unified and sorted data when overdueToggle is excluded
            return createUnifiedTrainingData()
        }
        // Return only overdue/upcoming trainings when toggle is available
        return trainingSessionDues || []
    }

    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions) &&
            !hasPermission('VIEW_MEMBER_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    // Get the unified training data
    const unifiedTrainingData = getUnifiedTrainingData()
    const isDataLoading = trainingListLoading || trainingSessionDuesLoading

    return (
        <div className="w-full">
            <div className="mb-5">
                <Card>
                    <TrainingListFilter
                        memberId={memberId}
                        onChange={handleFilterChange}
                        overdueSwitcher={!overdueBoolean}
                        excludeFilters={excludeFilters}
                    />
                </Card>
            </div>

            {isDataLoading ? (
                <div className="flex items-center justify-center p-8 text-muted-foreground">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading training data...
                </div>
            ) : unifiedTrainingData?.length > 0 ? (
                <FilteredTable
                    columns={createUnifiedTrainingColumns()}
                    data={unifiedTrainingData}
                    rowStatus={getTrainingRowStatus}
                    pageSize={20}
                    showToolbar={false}
                />
            ) : (
                <div className="group border-b">
                    <div className="p-4 col-span-4">
                        <div className="flex justify-between items-center gap-2 p-2 pt-4">
                            <p className="text-muted-foreground">
                                WOW! Look at that. All your crew are ship-shaped
                                and trained to the gills. Great job, captain!
                            </p>
                        </div>
                    </div>
                </div>
            )}

            {/* Optional toggle button for backward compatibility */}
            {!excludeFilters.includes('overdueToggle') && (
                <button
                    className="mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full"
                    onClick={() => toggleOverdueWrapper((prev) => !prev)}>
                    {overdueBoolean
                        ? 'View all completed trainings'
                        : 'View overdue trainings'}
                </button>
            )}
        </div>
    )
}

export default CrewTrainingList

export const OverdueTrainingList = ({
    trainingSessionDues,
    isVesselView = false,
    hideCrewColumn = false,
    pageSize = 20,
}: any) => {
    const isWide = useMediaQuery('(min-width: 720px)')

    const allColumns = createColumns([
        {
            accessorKey: 'title',
            header: 'Training',
            cell: ({ row }: { row: any }) => {
                const data: any = row.original
                return <MobileTrainingCard data={data} type="overdue" />
            },
        },
        {
            accessorKey: 'vessel',
            cellAlignment: 'left',
            header: 'Vessel',
            breakpoint: 'landscape',
            cell: ({ row }: { row: any }) => {
                const due = row.original
                return (
                    <>
                        {isVesselView == false && (
                            <div className="hidden md:table-cell p-2 align-top lg:align-middle items-center text-left">
                                {due.vessel?.title || ''}
                            </div>
                        )}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.vessel?.title || ''
                const valueB = rowB?.original?.vessel?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'crew',
            cellAlignment: 'right',
            header: 'Crew',
            breakpoint: 'laptop',
            cell: ({ row }: { row: any }) => {
                const due = row.original
                const members = due.members || []

                return isWide ? (
                    <div className="flex gap-1">
                        {members.map((member: any) => {
                            return (
                                <Tooltip key={member.id}>
                                    <TooltipTrigger>
                                        <Avatar
                                            size="sm"
                                            variant={
                                                due.status.isOverdue
                                                    ? 'destructive'
                                                    : 'secondary'
                                            }>
                                            <AvatarFallback className="text-sm">
                                                {getCrewInitials(
                                                    member.firstName,
                                                    member.surname,
                                                )}
                                            </AvatarFallback>
                                        </Avatar>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        {member.firstName}{' '}
                                        {member.surname ?? ''}
                                    </TooltipContent>
                                </Tooltip>
                            )
                        })}
                    </div>
                ) : (
                    <div
                        className={cn(
                            '!rounded-full size-10',
                            due.status?.class,
                        )}>
                        {members.length}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA =
                    `${rowA?.original?.members?.nodes?.[0]?.firstName ?? ''} ${rowA?.original?.members?.nodes?.[0]?.surname ?? ''}` ||
                    ''
                const valueB =
                    `${rowB?.original?.members?.nodes?.[0]?.firstName ?? ''} ${rowB?.original?.members?.nodes?.[0]?.surname ?? ''}` ||
                    ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'status',
            cellAlignment: 'right',
            header: 'Status',
            breakpoint: 'landscape',
            cell: ({ row }: { row: any }) => {
                const due = row.original
                return (
                    <div
                        className={`${due.status?.isOverdue ? due.status?.class : ''} rounded-md w-fit !p-2 text-nowrap`}>
                        {due.status?.label || 'Unknown Status'}
                    </div>
                )
            },
        },
    ])

    // Filter out crew column when hideCrewColumn is true
    const columns = hideCrewColumn
        ? allColumns.filter((col: any) => col.accessorKey !== 'crew')
        : allColumns

    return (
        <>
            {trainingSessionDues?.length > 0 ? (
                <DataTable
                    columns={columns}
                    data={trainingSessionDues}
                    pageSize={pageSize}
                    showToolbar={false}
                />
            ) : (
                <div className={`group border-b hover: `}>
                    <div className="p-4 col-span-4">
                        <div className="flex justify-between items-center gap-2 p-2 pt-4">
                            <p className="  ">
                                WOW! Look at that. All your crew are ship-shaped
                                and trained to the gills. Great job, captain!
                            </p>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
}
