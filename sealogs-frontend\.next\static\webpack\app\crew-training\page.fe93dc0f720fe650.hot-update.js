"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [overdueSwitcher, setOverdueSwitcher] = (0,nuqs__WEBPACK_IMPORTED_MODULE_16__.useQueryState)(\"overdue\");\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_17__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData)();\n    // Create a boolean state wrapper for the useTrainingFilters hook\n    const [overdueBoolean, setOverdueBoolean] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Sync the boolean state with the query state\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setOverdueBoolean(overdueSwitcher === \"true\");\n    }, [\n        overdueSwitcher\n    ]);\n    // Create a wrapper function that converts boolean to string for the query state\n    const toggleOverdueWrapper = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        if (typeof value === \"function\") {\n            setOverdueBoolean((prev)=>{\n                const newValue = value(prev);\n                setOverdueSwitcher(newValue ? \"true\" : \"false\");\n                return newValue;\n            });\n        } else {\n            setOverdueBoolean(value);\n            setOverdueSwitcher(value ? \"true\" : \"false\");\n        }\n    }, [\n        setOverdueSwitcher\n    ]);\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D Raw training sessions data from API:\", response.readTrainingSessions);\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            console.log(\"\\uD83D\\uDD04 Transformed training sessions data:\", transformedData);\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            if (transformedData) {\n                setTrainingList(transformedData);\n                setVesselIdOptions(vesselIDs);\n                setTrainingTypeIdOptions(trainingTypeIDs);\n                setTrainerIdOptions(trainerIDs);\n                setCrewIdOptions(memberIDs);\n            }\n            setPageInfo(response.readTrainingSessions.pageInfo);\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadTrainingSessionDues(filter);\n        loadTrainingList(newPage, filter);\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: toggleOverdueWrapper\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: overdueBoolean,\n            setOverdue: toggleOverdueWrapper\n        };\n    }, [\n        handleFilterChange,\n        overdueBoolean,\n        toggleOverdueWrapper\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D Raw training session dues data from API:\", response.readTrainingSessionDues);\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                console.log(\"\\uD83D\\uDD04 Processed training session dues:\", mergedDues);\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match unified structure\n    const transformCompletedTrainings = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                trainer: training.trainer,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                trainingLocationType: training.trainingLocationType,\n                // Add priority for sorting (higher number = higher priority)\n                priority: 0\n            };\n        });\n    };\n    // Create unified and sorted training data\n    const createUnifiedTrainingData = ()=>{\n        // Transform completed trainings\n        const transformedCompleted = transformCompletedTrainings(trainingList || []);\n        // Add priority to overdue/upcoming trainings\n        const prioritizedDues = (trainingSessionDues || []).map((due)=>{\n            var _due_status, _due_status1;\n            return {\n                ...due,\n                priority: ((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? 3 : ((_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.dueWithinSevenDays) ? 2 : 1\n            };\n        });\n        // Combine all training data\n        const allTrainings = [\n            ...prioritizedDues,\n            ...transformedCompleted\n        ];\n        // Sort by priority (descending), then by due date (ascending for overdue/upcoming, descending for completed)\n        return allTrainings.sort((a, b)=>{\n            // First sort by priority (overdue > upcoming > other > completed)\n            if (a.priority !== b.priority) {\n                return b.priority - a.priority;\n            }\n            // Within same priority, sort by date\n            const dateA = new Date(a.dueDate || 0).getTime();\n            const dateB = new Date(b.dueDate || 0).getTime();\n            // For overdue and upcoming (priority > 0), sort by due date ascending (earliest first)\n            if (a.priority > 0) {\n                return dateA - dateB;\n            }\n            // For completed trainings (priority = 0), sort by date descending (most recent first)\n            return dateB - dateA;\n        });\n    };\n    // Combined loading state for unified view\n    const isUnifiedDataLoading = excludeFilters.includes(\"overdueToggle\") ? trainingListLoading || trainingSessionDuesLoading : false;\n    // Row status evaluation for FilteredTable styling\n    const getTrainingRowStatus = (training)=>{\n        if (!training.status) return \"normal\";\n        if (training.status.isOverdue) {\n            return \"overdue\";\n        }\n        if (training.status.dueWithinSevenDays) {\n            return \"upcoming\";\n        }\n        return \"normal\";\n    };\n    // Unified column definitions for the training table\n    const createUnifiedTrainingColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: \"Training\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__.MobileTrainingCard, {\n                        data: training,\n                        memberId: memberId,\n                        type: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.label) === \"Completed\" ? \"completed\" : \"overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_trainingType, _rowB_original1;\n                    // Sort by priority first, then by training type name\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    if (priorityA !== priorityB) {\n                        return priorityB - priorityA;\n                    }\n                    const nameA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const nameB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return nameA.localeCompare(nameB);\n                }\n            },\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_trainingType, _training_trainingTypes_nodes, _training_trainingTypes, _training_status, _training_status1;\n                    const training = row.original;\n                    const trainingType = ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : _training_trainingTypes_nodes.map((t)=>t.title).join(\", \")) || \"\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-sm font-medium\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: trainingType\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_trainingType, _rowA_original, _rowB_original_trainingType, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_13__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Crew\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    const members = training.members || [];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 justify-center\",\n                        children: [\n                            members.slice(0, 5).map((member)=>/*#__PURE__*/ {\n                                var _training_status, _training_status1;\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                                size: \"sm\",\n                                                variant: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? \"destructive\" : ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) ? \"warning\" : \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 33\n                                }, undefined);\n                            }),\n                            members.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground ml-1\",\n                                children: [\n                                    \"+\",\n                                    members.length - 5\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_members_, _rowA_original_members, _rowA_original, _rowA_original_members_1, _rowA_original_members1, _rowA_original1, _rowB_original_members_, _rowB_original_members, _rowB_original, _rowB_original_members_1, _rowB_original_members1, _rowB_original1;\n                    var _rowA_original_members__firstName, _rowA_original_members__surname;\n                    const valueA = \"\".concat((_rowA_original_members__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_ = _rowA_original_members[0]) === null || _rowA_original_members_ === void 0 ? void 0 : _rowA_original_members_.firstName) !== null && _rowA_original_members__firstName !== void 0 ? _rowA_original_members__firstName : \"\", \" \").concat((_rowA_original_members__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_1 = _rowA_original_members1[0]) === null || _rowA_original_members_1 === void 0 ? void 0 : _rowA_original_members_1.surname) !== null && _rowA_original_members__surname !== void 0 ? _rowA_original_members__surname : \"\") || \"\";\n                    var _rowB_original_members__firstName, _rowB_original_members__surname;\n                    const valueB = \"\".concat((_rowB_original_members__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_ = _rowB_original_members[0]) === null || _rowB_original_members_ === void 0 ? void 0 : _rowB_original_members_.firstName) !== null && _rowB_original_members__firstName !== void 0 ? _rowB_original_members__firstName : \"\", \" \").concat((_rowB_original_members__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_1 = _rowB_original_members1[0]) === null || _rowB_original_members_1 === void 0 ? void 0 : _rowB_original_members_1.surname) !== null && _rowB_original_members__surname !== void 0 ? _rowB_original_members__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1, _training_status2, _training_status3;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"px-2 py-1 rounded-md text-xs font-medium text-center\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"bg-destructive/10 text-destructive\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"bg-warning/10 text-warning\", ((_training_status2 = training.status) === null || _training_status2 === void 0 ? void 0 : _training_status2.label) === \"Completed\" && \"bg-muted text-muted-foreground\"),\n                        children: ((_training_status3 = training.status) === null || _training_status3 === void 0 ? void 0 : _training_status3.label) || \"Unknown\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 624,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    return priorityB - priorityA;\n                }\n            },\n            {\n                accessorKey: \"dueDate\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1;\n                    const training = row.original;\n                    const dateText = formatDate(training.dueDate);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-sm font-medium text-right\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: dateText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 656,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || 0).getTime();\n                    const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || 0).getTime();\n                    return dateB - dateA;\n                }\n            }\n        ]);\n    // Get unified training data based on filter settings\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            // Use the new unified and sorted data when overdueToggle is excluded\n            return createUnifiedTrainingData();\n        }\n        // Return only overdue/upcoming trainings when toggle is available\n        return trainingSessionDues || [];\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 698,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 700,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Get the unified training data\n    const unifiedTrainingData = getUnifiedTrainingData();\n    const isDataLoading = trainingListLoading || trainingSessionDuesLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: !overdueBoolean,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 711,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 710,\n                columnNumber: 13\n            }, undefined),\n            isDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 21\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 722,\n                columnNumber: 17\n            }, undefined) : (unifiedTrainingData === null || unifiedTrainingData === void 0 ? void 0 : unifiedTrainingData.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.FilteredTable, {\n                columns: createUnifiedTrainingColumns(),\n                data: unifiedTrainingData,\n                rowStatus: getTrainingRowStatus,\n                pageSize: 20,\n                showToolbar: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 727,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 col-span-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 737,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 736,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 735,\n                columnNumber: 17\n            }, undefined),\n            !excludeFilters.includes(\"overdueToggle\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full\",\n                onClick: ()=>toggleOverdueWrapper((prev)=>!prev),\n                children: overdueBoolean ? \"View all completed trainings\" : \"View overdue trainings\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 749,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 709,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"1cTiu4uT+Z7rsblF/EYJ/7JtNv4=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_16__.useQueryState,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_17__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, isVesselView = false, hideCrewColumn = false, pageSize = 20 } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_17__.useMediaQuery)(\"(min-width: 720px)\");\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Training\",\n            cell: (param)=>{\n                let { row } = param;\n                const data = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__.MobileTrainingCard, {\n                    data: data,\n                    type: \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 777,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            cellAlignment: \"left\",\n            header: \"Vessel\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_vessel;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isVesselView == false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                        children: ((_due_vessel = due.vessel) === null || _due_vessel === void 0 ? void 0 : _due_vessel.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 790,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"crew\",\n            cellAlignment: \"right\",\n            header: \"Crew\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status;\n                const due = row.original;\n                const members = due.members || [];\n                return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: members.map((member)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                        size: \"sm\",\n                                        variant: due.status.isOverdue ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 33\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 813,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"!rounded-full size-10\", (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.class),\n                    children: members.length\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 842,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            cellAlignment: \"right\",\n            header: \"Status\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status, _due_status1, _due_status2;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? (_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                    children: ((_due_status2 = due.status) === null || _due_status2 === void 0 ? void 0 : _due_status2.label) || \"Unknown Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 869,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"crew\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n            columns: columns,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 886,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"  \",\n                        children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 896,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 895,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 894,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 893,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s1(OverdueTrainingList, \"mDuYwCIVI9QBRqOzz3Dco716Kgk=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_17__.useMediaQuery\n    ];\n});\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy9saXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLZ0M7QUFDYTtBQUNXO0FBRUE7QUFDSTtBQUMzQjtBQUN1QztBQUV4RSxpREFBaUQ7QUFDakQsTUFBTVcsYUFBYSxDQUFDQztJQUNoQixJQUFJLENBQUNBLFlBQVksT0FBTztJQUN4QixJQUFJO1FBQ0EsTUFBTUMsT0FBTyxJQUFJQyxLQUFLRjtRQUN0QixPQUFPSiw4RUFBTUEsQ0FBQ0ssTUFBTTtJQUN4QixFQUFFLFVBQU07UUFDSixPQUFPO0lBQ1g7QUFDSjtBQUNtQztBQU9BO0FBQ3NDO0FBQ25DO0FBU2Q7QUFDdUI7QUFDWDtBQUMyQjtBQUMzQjtBQUNXO0FBQ2lCO0FBRU07QUFFdEUsTUFBTXNCLG1CQUFtQjtRQUFDLEVBQ3RCQyxXQUFXLENBQUMsRUFDWkMsV0FBVyxDQUFDLEVBQ1pDLGNBQWMsRUFDZEMsaUJBQWlCLEVBQUUsRUFNdEI7O0lBQ0csTUFBTUMsUUFBUTtJQUNkLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHckMsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDc0MsVUFBVUMsWUFBWSxHQUFHdkMsK0NBQVFBLENBQUM7UUFDckN3QyxZQUFZO1FBQ1pDLGFBQWE7UUFDYkMsaUJBQWlCO0lBQ3JCO0lBQ0EsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBRzVDLCtDQUFRQSxDQUFDLEVBQUU7SUFDbkQsTUFBTSxDQUFDNkMscUJBQXFCQyx1QkFBdUIsR0FBRzlDLCtDQUFRQSxDQUFDLEVBQUU7SUFDakUsTUFBTSxDQUFDK0MsTUFBTUMsUUFBUSxHQUFHaEQsK0NBQVFBLENBQUM7SUFDakMsTUFBTSxDQUFDaUQsaUJBQWlCQyxtQkFBbUIsR0FBR2xELCtDQUFRQSxDQUFDLEVBQUU7SUFDekQsTUFBTSxDQUFDbUQsdUJBQXVCQyx5QkFBeUIsR0FBR3BELCtDQUFRQSxDQUM5RCxFQUFFO0lBRU4sTUFBTSxDQUFDcUQsa0JBQWtCQyxvQkFBb0IsR0FBR3RELCtDQUFRQSxDQUFDLEVBQUU7SUFDM0QsTUFBTSxDQUFDdUQsZUFBZUMsaUJBQWlCLEdBQUd4RCwrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3JELE1BQU0sQ0FBQ3lELGlCQUFpQkMsbUJBQW1CLEdBQUdoQyxvREFBYUEsQ0FBQztJQUM1RCxNQUFNaUMsU0FBU3BDLCtEQUFhQSxDQUFDO0lBQzdCLE1BQU0sRUFBRXFDLGlCQUFpQixFQUFFQyxTQUFTQyxpQkFBaUIsRUFBRSxHQUNuRGxDLCtFQUFpQkE7SUFFckIsaUVBQWlFO0lBQ2pFLE1BQU0sQ0FBQ21DLGdCQUFnQkMsa0JBQWtCLEdBQUdoRSwrQ0FBUUEsQ0FBVTtJQUU5RCw4Q0FBOEM7SUFDOUNELGdEQUFTQSxDQUFDO1FBQ05pRSxrQkFBa0JQLG9CQUFvQjtJQUMxQyxHQUFHO1FBQUNBO0tBQWdCO0lBRXBCLGdGQUFnRjtJQUNoRixNQUFNUSx1QkFBdUJuRSxrREFBV0EsQ0FDcEMsQ0FBQ29FO1FBQ0csSUFBSSxPQUFPQSxVQUFVLFlBQVk7WUFDN0JGLGtCQUFrQixDQUFDRztnQkFDZixNQUFNQyxXQUFXRixNQUFNQztnQkFDdkJULG1CQUFtQlUsV0FBVyxTQUFTO2dCQUN2QyxPQUFPQTtZQUNYO1FBQ0osT0FBTztZQUNISixrQkFBa0JFO1lBQ2xCUixtQkFBbUJRLFFBQVEsU0FBUztRQUN4QztJQUNKLEdBQ0E7UUFBQ1I7S0FBbUI7SUFHeEIsTUFBTSxDQUFDVyxtQkFBbUIsRUFBRVIsU0FBU1MsbUJBQW1CLEVBQUUsQ0FBQyxHQUFHekUsNkRBQVlBLENBQ3RFRixxRUFBaUJBLEVBQ2pCO1FBQ0k0RSxhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVkMsUUFBUUMsR0FBRyxDQUNQLHFEQUNBRixTQUFTRyxvQkFBb0I7WUFFakMsTUFBTUMsT0FBT0osU0FBU0csb0JBQW9CLENBQUNFLEtBQUs7WUFFaEQsNkVBQTZFO1lBQzdFLE1BQU1DLGtCQUFrQkYsS0FBS0csR0FBRyxDQUFDLENBQUNDO2dCQUM5QixNQUFNQyxxQkFBcUJ0QixrQkFDdkJxQixLQUFLRSxNQUFNLENBQUNDLEVBQUUsRUFDZEgsS0FBS0UsTUFBTTtnQkFHZixPQUFPO29CQUNILEdBQUdGLElBQUk7b0JBQ1BFLFFBQVFEO2dCQUNaO1lBQ0o7WUFFQVIsUUFBUUMsR0FBRyxDQUNQLG9EQUNBSTtZQUdKLE1BQU1NLFlBQVlDLE1BQU1DLElBQUksQ0FDeEIsSUFBSUMsSUFBSVgsS0FBS0csR0FBRyxDQUFDLENBQUNDLE9BQWNBLEtBQUtFLE1BQU0sQ0FBQ0MsRUFBRSxJQUNoREssTUFBTSxDQUFDLENBQUNMLEtBQVksQ0FBQ0EsT0FBTztZQUM5QixNQUFNTSxrQkFBa0JKLE1BQU1DLElBQUksQ0FDOUIsSUFBSUMsSUFDQVgsS0FBS2MsT0FBTyxDQUFDLENBQUNWLE9BQ1ZBLEtBQUtXLGFBQWEsQ0FBQ2QsS0FBSyxDQUFDRSxHQUFHLENBQUMsQ0FBQ2EsSUFBV0EsRUFBRVQsRUFBRTtZQUl6RCxNQUFNVSxhQUFhUixNQUFNQyxJQUFJLENBQ3pCLElBQUlDLElBQUlYLEtBQUtHLEdBQUcsQ0FBQyxDQUFDQyxPQUFjQSxLQUFLYyxTQUFTLElBQ2hETixNQUFNLENBQUMsQ0FBQ0wsS0FBWSxDQUFDQSxPQUFPO1lBQzlCLE1BQU1ZLFlBQVlWLE1BQU1DLElBQUksQ0FDeEIsSUFBSUMsSUFDQVgsS0FBS2MsT0FBTyxDQUFDLENBQUNWLE9BQ1ZBLEtBQUtnQixPQUFPLENBQUNuQixLQUFLLENBQUNFLEdBQUcsQ0FBQyxDQUFDYSxJQUFXQSxFQUFFVCxFQUFFO1lBS25ELElBQUlMLGlCQUFpQjtnQkFDakJuQyxnQkFBZ0JtQztnQkFDaEI3QixtQkFBbUJtQztnQkFDbkJqQyx5QkFBeUJzQztnQkFDekJwQyxvQkFBb0J3QztnQkFDcEJ0QyxpQkFBaUJ3QztZQUNyQjtZQUNBekQsWUFBWWtDLFNBQVNHLG9CQUFvQixDQUFDdEMsUUFBUTtRQUN0RDtRQUNBNEQsU0FBUyxDQUFDQztZQUNOekIsUUFBUXlCLEtBQUssQ0FBQywyQkFBMkJBO1FBQzdDO0lBQ0o7SUFHSixNQUFNQyxtQkFBbUI7WUFDckJDLDZFQUFvQixHQUNwQkMsZ0ZBQW9CO1lBQUUsR0FBR2IsTUFBTTtRQUFDO1FBRWhDLE1BQU1wQixrQkFBa0I7WUFDcEJrQyxXQUFXO2dCQUNQZCxRQUFRYTtnQkFDUkUsUUFBUUgsWUFBWWxFO2dCQUNwQkEsT0FBT0E7WUFDWDtRQUNKO0lBQ0o7SUFFQSxNQUFNc0UsMEJBQTBCLE9BQU9oQjtRQUNuQyxNQUFNaUIsWUFBaUIsQ0FBQztRQUN4QixJQUFJM0UsV0FBVyxHQUFHO1lBQ2QyRSxVQUFVQyxRQUFRLEdBQUc7Z0JBQUVDLElBQUksQ0FBQzdFO1lBQVM7UUFDekM7UUFDQSxJQUFJQyxXQUFXLEdBQUc7WUFDZDBFLFVBQVVHLFFBQVEsR0FBRztnQkFBRUQsSUFBSSxDQUFDNUU7WUFBUztRQUN6QztRQUNBLElBQUl5RCxPQUFPb0IsUUFBUSxFQUFFO1lBQ2pCSCxVQUFVRyxRQUFRLEdBQUdwQixPQUFPb0IsUUFBUTtRQUN4QztRQUNBLElBQUlwQixPQUFPRyxhQUFhLEVBQUU7WUFDdEJjLFVBQVVJLGNBQWMsR0FBRztnQkFBRUYsSUFBSW5CLE9BQU9HLGFBQWEsQ0FBQ1IsRUFBRSxDQUFDMkIsUUFBUTtZQUFDO1FBQ3RFO1FBQ0EsSUFBSXRCLE9BQU9RLE9BQU8sRUFBRTtZQUNoQlMsVUFBVUMsUUFBUSxHQUFHO2dCQUFFQyxJQUFJbkIsT0FBT1EsT0FBTyxDQUFDYixFQUFFLENBQUMyQixRQUFRO1lBQUM7UUFDMUQ7UUFDQSxJQUFJdEIsT0FBT2pGLElBQUksRUFBRTtZQUNia0csVUFBVU0sT0FBTyxHQUFHdkIsT0FBT2pGLElBQUk7UUFDbkMsT0FBTztZQUNIa0csVUFBVU0sT0FBTyxHQUFHO2dCQUFFQyxJQUFJO1lBQUs7UUFDbkM7UUFDQSxNQUFNQyx3QkFBd0I7WUFDMUJYLFdBQVc7Z0JBQ1BkLFFBQVFpQjtZQUNaO1FBQ0o7SUFDSjtJQUVBLE1BQU1TLHdCQUF3QixDQUFDQztRQUMzQixJQUFJQSxVQUFVLEtBQUtBLFlBQVlyRSxNQUFNO1FBQ3JDQyxRQUFRb0U7UUFDUlgsd0JBQXdCaEI7UUFDeEJXLGlCQUFpQmdCLFNBQVMzQjtJQUM5QjtJQUVBLE1BQU0sRUFDRkEsTUFBTSxFQUNONEIsU0FBUyxFQUNUQyxrQkFBa0IsRUFDckIsR0FBRzdGLDhFQUFrQkEsQ0FBQztRQUNuQjhGLGVBQWUsQ0FBQztRQUNoQkMsVUFBVXBCO1FBQ1ZxQixVQUFVaEI7UUFDVmlCLGVBQWV6RDtJQUNuQjtJQUVBbEUsZ0RBQVNBLENBQUM7UUFDTixJQUFJa0MsZ0JBQ0FBLGVBQWUwRixPQUFPLEdBQUc7WUFDckJDLE9BQU9OO1lBQ1BPLFNBQVM5RDtZQUNUK0QsWUFBWTdEO1FBQ2hCO0lBQ1IsR0FBRztRQUFDcUQ7UUFBb0J2RDtRQUFnQkU7S0FBcUI7SUFFN0QsTUFBTSxDQUFDaUQseUJBQXlCLEVBQUVyRCxTQUFTa0UsMEJBQTBCLEVBQUUsQ0FBQyxHQUNwRWxJLDZEQUFZQSxDQUFDRCw4RUFBMEJBLEVBQUU7UUFDckMyRSxhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVkMsUUFBUUMsR0FBRyxDQUNQLHlEQUNBRixTQUFTeUMsdUJBQXVCO1lBRXBDLE1BQU1yQyxPQUFPSixTQUFTeUMsdUJBQXVCLENBQUNwQyxLQUFLO1lBQ25ELElBQUlELE1BQU07Z0JBQ04sb0VBQW9FO2dCQUNwRSxNQUFNbUQsZUFBZW5ELEtBQUtZLE1BQU0sQ0FBQyxDQUFDUixPQUM5QkEsS0FBS0UsTUFBTSxDQUFDOEMsY0FBYyxDQUFDbkQsS0FBSyxDQUFDb0QsSUFBSSxDQUFDLENBQUNDO3dCQUNuQyxPQUFPQSxFQUFFL0MsRUFBRSxLQUFLSCxLQUFLMEIsUUFBUTtvQkFDakM7Z0JBRUosTUFBTXlCLGdCQUFnQkosYUFBYWhELEdBQUcsQ0FBQyxDQUFDcUQ7b0JBQ3BDLE9BQU87d0JBQUUsR0FBR0EsR0FBRzt3QkFBRUMsUUFBUXBJLDBFQUF3QkEsQ0FBQ21JO29CQUFLO2dCQUMzRDtnQkFDQSw0Q0FBNEM7Z0JBQzVDLHNEQUFzRDtnQkFDdEQsdUJBQXVCO2dCQUN2QixtQkFBbUI7Z0JBQ25CLHVDQUF1QztnQkFDdkMsa0RBQWtEO2dCQUNsRCwyREFBMkQ7Z0JBQzNELFlBQVk7Z0JBQ1osU0FBUztnQkFDVCxJQUFJO2dCQUNKLG9EQUFvRDtnQkFDcEQsTUFBTUUsY0FBY0gsY0FBY0ksTUFBTSxDQUNwQyxDQUFDQyxLQUFVSjtvQkFDUCxNQUFNSyxNQUFNLEdBQW1CTCxPQUFoQkEsSUFBSXhCLFFBQVEsRUFBQyxLQUF5QndCLE9BQXRCQSxJQUFJdkIsY0FBYyxFQUFDLEtBQWUsT0FBWnVCLElBQUlyQixPQUFPO29CQUNoRSxJQUFJLENBQUN5QixHQUFHLENBQUNDLElBQUksRUFBRTt3QkFDWEQsR0FBRyxDQUFDQyxJQUFJLEdBQUc7NEJBQ1B0RCxJQUFJaUQsSUFBSWpELEVBQUU7NEJBQ1Z5QixVQUFVd0IsSUFBSXhCLFFBQVE7NEJBQ3RCMUIsUUFBUWtELElBQUlsRCxNQUFNOzRCQUNsQjJCLGdCQUFnQnVCLElBQUl2QixjQUFjOzRCQUNsQzZCLGNBQWNOLElBQUlNLFlBQVk7NEJBQzlCM0IsU0FBU3FCLElBQUlyQixPQUFPOzRCQUNwQnNCLFFBQVFELElBQUlDLE1BQU07NEJBQ2xCTSxzQkFDSVAsSUFBSVEsZUFBZSxDQUNkRCxvQkFBb0I7NEJBQzdCM0MsU0FBUyxFQUFFO3dCQUNmO29CQUNKO29CQUNBd0MsR0FBRyxDQUFDQyxJQUFJLENBQUN6QyxPQUFPLENBQUM2QyxJQUFJLENBQUNULElBQUlVLE1BQU07b0JBQ2hDLE9BQU9OO2dCQUNYLEdBQ0EsQ0FBQztnQkFHTCxNQUFNTyxhQUFhQyxPQUFPQyxNQUFNLENBQUNYLGFBQWF2RCxHQUFHLENBQzdDLENBQUNtRTtvQkFDRyxNQUFNQyxnQkFBZ0JELE1BQU1sRCxPQUFPLENBQUN1QyxNQUFNLENBQ3RDLENBQUNDLEtBQVVNO3dCQUNQLE1BQU1NLGlCQUFpQlosSUFBSWEsSUFBSSxDQUMzQixDQUFDbkIsSUFBV0EsRUFBRS9DLEVBQUUsS0FBSzJELE9BQU8zRCxFQUFFO3dCQUVsQyxJQUFJaUUsZ0JBQWdCOzRCQUNoQkEsZUFBZUUsU0FBUyxHQUNwQlIsT0FBT1EsU0FBUzs0QkFDcEJGLGVBQWVHLE9BQU8sR0FBR1QsT0FBT1MsT0FBTzt3QkFDM0MsT0FBTzs0QkFDSGYsSUFBSUssSUFBSSxDQUFDQzt3QkFDYjt3QkFDQSxPQUFPTjtvQkFDWCxHQUNBLEVBQUU7b0JBRU4sT0FBTzt3QkFDSHJELElBQUkrRCxNQUFNL0QsRUFBRTt3QkFDWnlCLFVBQVVzQyxNQUFNdEMsUUFBUTt3QkFDeEIxQixRQUFRZ0UsTUFBTWhFLE1BQU07d0JBQ3BCMkIsZ0JBQWdCcUMsTUFBTXJDLGNBQWM7d0JBQ3BDNkIsY0FBY1EsTUFBTVIsWUFBWTt3QkFDaENMLFFBQVFhLE1BQU1iLE1BQU07d0JBQ3BCdEIsU0FBU21DLE1BQU1uQyxPQUFPO3dCQUN0QjRCLHNCQUNJTyxNQUFNUCxvQkFBb0I7d0JBQzlCM0MsU0FBU21EO29CQUNiO2dCQUNKO2dCQUVKMUUsUUFBUUMsR0FBRyxDQUNQLGlEQUNBcUU7Z0JBRUpsRyx1QkFBdUJrRztZQUMzQjtRQUNKO1FBQ0E5QyxTQUFTLENBQUNDO1lBQ056QixRQUFReUIsS0FBSyxDQUFDLGlDQUFpQ0E7UUFDbkQ7SUFDSjtJQUVKcEcsZ0RBQVNBLENBQUM7UUFDTixJQUFJcUMsV0FBVztZQUNYLE1BQU1xSCxJQUF1QjtnQkFBRSxHQUFHaEUsTUFBTTtZQUFDO1lBQ3pDLElBQUksQ0FBQzFELFdBQVcsR0FBRztnQkFDZjBILEVBQUV4RCxPQUFPLEdBQUc7b0JBQUViLElBQUk7d0JBQUUyQixVQUFVLENBQUNoRjtvQkFBUztnQkFBRTtZQUM5QztZQUNBc0YsVUFBVW9DO1lBQ1ZoRCx3QkFBd0JnRDtZQUN4QnJELGlCQUFpQixHQUFHcUQ7WUFDcEJwSCxhQUFhO1FBQ2pCO0lBQ0osR0FBRztRQUFDRDtLQUFVO0lBRWQsTUFBTSxDQUFDc0gsYUFBYUMsZUFBZSxHQUFHM0osK0NBQVFBLENBQU07SUFFcERELGdEQUFTQSxDQUFDO1FBQ040SixlQUFldkosbUVBQWNBO0lBQ2pDLEdBQUcsRUFBRTtJQUVMLG1FQUFtRTtJQUNuRSxNQUFNd0osOEJBQThCLENBQUNqSDtRQUNqQyxPQUFPQSxhQUFhcUMsR0FBRyxDQUFDLENBQUM2RTtnQkFHakJBLGtCQU9VQSxtQkFFTUEsZ0NBQUFBLCtCQUFBQSx5QkFDRkEsZ0NBQUFBLDBCQUdMQTtZQWZiLHFEQUFxRDtZQUNyRCxNQUFNM0UscUJBQXFCdEIsbUJBQ3ZCaUcsbUJBQUFBLFNBQVMxRSxNQUFNLGNBQWYwRSx1Q0FBQUEsaUJBQWlCekUsRUFBRSxFQUNuQnlFLFNBQVMxRSxNQUFNO1lBR25CLE9BQU87Z0JBQ0hDLElBQUl5RSxTQUFTekUsRUFBRTtnQkFDZjRCLFNBQVM2QyxTQUFTckosSUFBSTtnQkFDdEJxRyxRQUFRLEdBQUVnRCxvQkFBQUEsU0FBUzFFLE1BQU0sY0FBZjBFLHdDQUFBQSxrQkFBaUJ6RSxFQUFFO2dCQUM3QkQsUUFBUUQ7Z0JBQ1I0QixjQUFjLEdBQUUrQywwQkFBQUEsU0FBU2pFLGFBQWEsY0FBdEJpRSwrQ0FBQUEsZ0NBQUFBLHdCQUF3Qi9FLEtBQUssY0FBN0IrRSxxREFBQUEsaUNBQUFBLDZCQUErQixDQUFDLEVBQUUsY0FBbENBLHFEQUFBQSwrQkFBb0N6RSxFQUFFO2dCQUN0RHVELGNBQWNrQixFQUFBQSwyQkFBQUEsU0FBU2pFLGFBQWEsY0FBdEJpRSxnREFBQUEsaUNBQUFBLHlCQUF3Qi9FLEtBQUssY0FBN0IrRSxxREFBQUEsOEJBQStCLENBQUMsRUFBRSxLQUFJO29CQUNoREMsT0FBTztnQkFDWDtnQkFDQTdELFNBQVM0RCxFQUFBQSxvQkFBQUEsU0FBUzVELE9BQU8sY0FBaEI0RCx3Q0FBQUEsa0JBQWtCL0UsS0FBSyxLQUFJLEVBQUU7Z0JBQ3RDaUYsU0FBU0YsU0FBU0UsT0FBTztnQkFDekJ6QixRQUFRO29CQUNKMEIsT0FBTztvQkFDUEMsV0FBVztvQkFDWEMsT0FBTztvQkFDUEMsb0JBQW9CO2dCQUN4QjtnQkFDQXZCLHNCQUFzQmlCLFNBQVNqQixvQkFBb0I7Z0JBQ25ELDZEQUE2RDtnQkFDN0R3QixVQUFVO1lBQ2Q7UUFDSjtJQUNKO0lBRUEsMENBQTBDO0lBQzFDLE1BQU1DLDRCQUE0QjtRQUM5QixnQ0FBZ0M7UUFDaEMsTUFBTUMsdUJBQXVCViw0QkFDekJqSCxnQkFBZ0IsRUFBRTtRQUd0Qiw2Q0FBNkM7UUFDN0MsTUFBTTRILGtCQUFrQixDQUFDMUgsdUJBQXVCLEVBQUUsRUFBRW1DLEdBQUcsQ0FBQyxDQUFDcUQ7Z0JBRTNDQSxhQUVKQTttQkFKNkQ7Z0JBQ25FLEdBQUdBLEdBQUc7Z0JBQ04rQixVQUFVL0IsRUFBQUEsY0FBQUEsSUFBSUMsTUFBTSxjQUFWRCxrQ0FBQUEsWUFBWTRCLFNBQVMsSUFDekIsSUFDQTVCLEVBQUFBLGVBQUFBLElBQUlDLE1BQU0sY0FBVkQsbUNBQUFBLGFBQVk4QixrQkFBa0IsSUFDNUIsSUFDQTtZQUNaOztRQUVBLDRCQUE0QjtRQUM1QixNQUFNSyxlQUFlO2VBQUlEO2VBQW9CRDtTQUFxQjtRQUVsRSw2R0FBNkc7UUFDN0csT0FBT0UsYUFBYUMsSUFBSSxDQUFDLENBQUNDLEdBQVFDO1lBQzlCLGtFQUFrRTtZQUNsRSxJQUFJRCxFQUFFTixRQUFRLEtBQUtPLEVBQUVQLFFBQVEsRUFBRTtnQkFDM0IsT0FBT08sRUFBRVAsUUFBUSxHQUFHTSxFQUFFTixRQUFRO1lBQ2xDO1lBRUEscUNBQXFDO1lBQ3JDLE1BQU1RLFFBQVEsSUFBSW5LLEtBQUtpSyxFQUFFMUQsT0FBTyxJQUFJLEdBQUc2RCxPQUFPO1lBQzlDLE1BQU1DLFFBQVEsSUFBSXJLLEtBQUtrSyxFQUFFM0QsT0FBTyxJQUFJLEdBQUc2RCxPQUFPO1lBRTlDLHVGQUF1RjtZQUN2RixJQUFJSCxFQUFFTixRQUFRLEdBQUcsR0FBRztnQkFDaEIsT0FBT1EsUUFBUUU7WUFDbkI7WUFFQSxzRkFBc0Y7WUFDdEYsT0FBT0EsUUFBUUY7UUFDbkI7SUFDSjtJQUVBLDBDQUEwQztJQUMxQyxNQUFNRyx1QkFBdUI3SSxlQUFlOEksUUFBUSxDQUFDLG1CQUMvQzFHLHVCQUF1QnlELDZCQUN2QjtJQUVOLGtEQUFrRDtJQUNsRCxNQUFNa0QsdUJBQXVCLENBQ3pCcEI7UUFFQSxJQUFJLENBQUNBLFNBQVN2QixNQUFNLEVBQUUsT0FBTztRQUU3QixJQUFJdUIsU0FBU3ZCLE1BQU0sQ0FBQzJCLFNBQVMsRUFBRTtZQUMzQixPQUFPO1FBQ1g7UUFFQSxJQUFJSixTQUFTdkIsTUFBTSxDQUFDNkIsa0JBQWtCLEVBQUU7WUFDcEMsT0FBTztRQUNYO1FBRUEsT0FBTztJQUNYO0lBRUEsb0RBQW9EO0lBQ3BELE1BQU1lLCtCQUErQixJQUNqQ3ZLLHdFQUFhQSxDQUFDO1lBQ1Y7Z0JBQ0l3SyxhQUFhO2dCQUNiQyxRQUFRO2dCQUNSQyxNQUFNO3dCQUFDLEVBQUVDLEdBQUcsRUFBZ0I7d0JBT1p6QjtvQkFOWixNQUFNQSxXQUFnQnlCLElBQUlDLFFBQVE7b0JBQ2xDLHFCQUNJLDhEQUFDMUosaUZBQWtCQTt3QkFDZmdELE1BQU1nRjt3QkFDTjlILFVBQVVBO3dCQUNWeUosTUFDSTNCLEVBQUFBLG1CQUFBQSxTQUFTdkIsTUFBTSxjQUFmdUIsdUNBQUFBLGlCQUFpQkcsS0FBSyxNQUFLLGNBQ3JCLGNBQ0E7Ozs7OztnQkFJdEI7Z0JBQ0F5QixXQUFXLENBQUNDLE1BQVdDO3dCQUVERCxnQkFDQUMsZ0JBTUpELDZCQUFBQSxpQkFDQUMsNkJBQUFBO29CQVRkLHFEQUFxRDtvQkFDckQsTUFBTUMsWUFBWUYsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyxxQ0FBQUEsZUFBZ0J0QixRQUFRLEtBQUk7b0JBQzlDLE1BQU15QixZQUFZRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSixRQUFRLGNBQWRJLHFDQUFBQSxlQUFnQnZCLFFBQVEsS0FBSTtvQkFFOUMsSUFBSXdCLGNBQWNDLFdBQVc7d0JBQ3pCLE9BQU9BLFlBQVlEO29CQUN2QjtvQkFFQSxNQUFNRSxRQUFRSixDQUFBQSxpQkFBQUEsNEJBQUFBLGtCQUFBQSxLQUFNSCxRQUFRLGNBQWRHLHVDQUFBQSw4QkFBQUEsZ0JBQWdCL0MsWUFBWSxjQUE1QitDLGtEQUFBQSw0QkFBOEI1QixLQUFLLEtBQUk7b0JBQ3JELE1BQU1pQyxRQUFRSixDQUFBQSxpQkFBQUEsNEJBQUFBLGtCQUFBQSxLQUFNSixRQUFRLGNBQWRJLHVDQUFBQSw4QkFBQUEsZ0JBQWdCaEQsWUFBWSxjQUE1QmdELGtEQUFBQSw0QkFBOEI3QixLQUFLLEtBQUk7b0JBQ3JELE9BQU9nQyxNQUFNRSxhQUFhLENBQUNEO2dCQUMvQjtZQUNKO1lBQ0E7Z0JBQ0laLGFBQWE7Z0JBQ2JjLGVBQWU7Z0JBQ2ZiLFFBQVE7d0JBQUMsRUFBRWMsTUFBTSxFQUFtQjt5Q0FDaEMsOERBQUNwTCxtRkFBbUJBO3dCQUNoQm9MLFFBQVFBO3dCQUNScEMsT0FBTTs7Ozs7OztnQkFHZHFDLFlBQVk7Z0JBQ1pkLE1BQU07d0JBQUMsRUFBRUMsR0FBRyxFQUFnQjt3QkFHcEJ6Qix3QkFDQUEsK0JBQUFBLHlCQVNRQSxrQkFFQUE7b0JBZFosTUFBTUEsV0FBV3lCLElBQUlDLFFBQVE7b0JBQzdCLE1BQU01QyxlQUNGa0IsRUFBQUEseUJBQUFBLFNBQVNsQixZQUFZLGNBQXJCa0IsNkNBQUFBLHVCQUF1QkMsS0FBSyxPQUM1QkQsMEJBQUFBLFNBQVNqRSxhQUFhLGNBQXRCaUUsK0NBQUFBLGdDQUFBQSx3QkFBd0IvRSxLQUFLLGNBQTdCK0Usb0RBQUFBLDhCQUNNN0UsR0FBRyxDQUFDLENBQUNhLElBQVdBLEVBQUVpRSxLQUFLLEVBQ3hCc0MsSUFBSSxDQUFDLFVBQ1Y7b0JBRUoscUJBQ0ksOERBQUNDO3dCQUNHQyxXQUFXOUssbURBQUVBLENBQ1QsdUJBQ0FxSSxFQUFBQSxtQkFBQUEsU0FBU3ZCLE1BQU0sY0FBZnVCLHVDQUFBQSxpQkFBaUJJLFNBQVMsS0FDdEIsNkNBQ0pKLEVBQUFBLG9CQUFBQSxTQUFTdkIsTUFBTSxjQUFmdUIsd0NBQUFBLGtCQUFpQk0sa0JBQWtCLEtBQy9CO2tDQUVQeEI7Ozs7OztnQkFHYjtnQkFDQThDLFdBQVcsQ0FBQ0MsTUFBV0M7d0JBQ0pELDZCQUFBQSxnQkFDQUMsNkJBQUFBO29CQURmLE1BQU1ZLFNBQVNiLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcsc0NBQUFBLDhCQUFBQSxlQUFnQi9DLFlBQVksY0FBNUIrQyxrREFBQUEsNEJBQThCNUIsS0FBSyxLQUFJO29CQUN0RCxNQUFNMEMsU0FBU2IsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUosUUFBUSxjQUFkSSxzQ0FBQUEsOEJBQUFBLGVBQWdCaEQsWUFBWSxjQUE1QmdELGtEQUFBQSw0QkFBOEI3QixLQUFLLEtBQUk7b0JBQ3RELE9BQU95QyxPQUFPUCxhQUFhLENBQUNRO2dCQUNoQztZQUNKO1lBQ0E7Z0JBQ0lyQixhQUFhO2dCQUNiYyxlQUFlO2dCQUNmYixRQUFRO3dCQUFDLEVBQUVjLE1BQU0sRUFBbUI7eUNBQ2hDLDhEQUFDcEwsbUZBQW1CQTt3QkFBQ29MLFFBQVFBO3dCQUFRcEMsT0FBTTs7Ozs7OztnQkFFL0NxQyxZQUFZO2dCQUNaZCxNQUFNO3dCQUFDLEVBQUVDLEdBQUcsRUFBZ0I7d0JBS1h6QjtvQkFKYixNQUFNQSxXQUFXeUIsSUFBSUMsUUFBUTtvQkFDN0IscUJBQ0ksOERBQUNjO3dCQUFJQyxXQUFVOzswQ0FDWCw4REFBQ0c7Z0NBQUtILFdBQVU7MENBQ1h6QyxFQUFBQSxtQkFBQUEsU0FBUzFFLE1BQU0sY0FBZjBFLHVDQUFBQSxpQkFBaUJDLEtBQUssS0FBSTs7Ozs7OzBDQUUvQiw4REFBQ25JLHlEQUFhQTtnQ0FDVndELFFBQVEwRSxTQUFTMUUsTUFBTTtnQ0FDdkJ1SCxlQUFjOzs7Ozs7Ozs7Ozs7Z0JBSTlCO2dCQUNBakIsV0FBVyxDQUFDQyxNQUFXQzt3QkFDSkQsdUJBQUFBLGdCQUNBQyx1QkFBQUE7b0JBRGYsTUFBTVksU0FBU2IsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyxzQ0FBQUEsd0JBQUFBLGVBQWdCdkcsTUFBTSxjQUF0QnVHLDRDQUFBQSxzQkFBd0I1QixLQUFLLEtBQUk7b0JBQ2hELE1BQU0wQyxTQUFTYixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSixRQUFRLGNBQWRJLHNDQUFBQSx3QkFBQUEsZUFBZ0J4RyxNQUFNLGNBQXRCd0csNENBQUFBLHNCQUF3QjdCLEtBQUssS0FBSTtvQkFDaEQsT0FBT3lDLE9BQU9QLGFBQWEsQ0FBQ1E7Z0JBQ2hDO1lBQ0o7WUFDQTtnQkFDSXJCLGFBQWE7Z0JBQ2JjLGVBQWU7Z0JBQ2ZiLFFBQVE7d0JBQUMsRUFBRWMsTUFBTSxFQUFtQjt5Q0FDaEMsOERBQUNwTCxtRkFBbUJBO3dCQUFDb0wsUUFBUUE7d0JBQVFwQyxPQUFNOzs7Ozs7O2dCQUUvQ3FDLFlBQVk7Z0JBQ1pkLE1BQU07d0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFDeEIsTUFBTXpCLFdBQVd5QixJQUFJQyxRQUFRO29CQUM3QixNQUFNdEYsVUFBVTRELFNBQVM1RCxPQUFPLElBQUksRUFBRTtvQkFFdEMscUJBQ0ksOERBQUNvRzt3QkFBSUMsV0FBVTs7NEJBQ1ZyRyxRQUFRMEcsS0FBSyxDQUFDLEdBQUcsR0FBRzNILEdBQUcsQ0FBQyxDQUFDK0Q7b0NBTU5jLGtCQUVNQTtvQ0FlYmQ7dUNBdEJULDhEQUFDM0gsb0RBQU9BOztzREFDSiw4REFBQ0UsMkRBQWNBO3NEQUNYLDRFQUFDTixtREFBTUE7Z0RBQ0g0TCxNQUFLO2dEQUNMQyxTQUNJaEQsRUFBQUEsbUJBQUFBLFNBQVN2QixNQUFNLGNBQWZ1Qix1Q0FBQUEsaUJBQWlCSSxTQUFTLElBQ3BCLGdCQUNBSixFQUFBQSxvQkFBQUEsU0FBU3ZCLE1BQU0sY0FBZnVCLHdDQUFBQSxrQkFDUU0sa0JBQWtCLElBQ3hCLFlBQ0E7MERBRVosNEVBQUNsSiwyREFBY0E7b0RBQUNxTCxXQUFVOzhEQUNyQm5MLGdFQUFlQSxDQUNaNEgsT0FBT1EsU0FBUyxFQUNoQlIsT0FBT1MsT0FBTzs7Ozs7Ozs7Ozs7Ozs7OztzREFLOUIsOERBQUNuSSwyREFBY0E7O2dEQUNWMEgsT0FBT1EsU0FBUztnREFBRTtnREFDbEJSLENBQUFBLGtCQUFBQSxPQUFPUyxPQUFPLGNBQWRULDZCQUFBQSxrQkFBa0I7Ozs7Ozs7O21DQXRCYkEsT0FBTzNELEVBQUU7Ozs7OzRCQXdCZDs0QkFFWmEsUUFBUTZHLE1BQU0sR0FBRyxtQkFDZCw4REFBQ1Q7Z0NBQUlDLFdBQVU7O29DQUFxQztvQ0FDOUNyRyxRQUFRNkcsTUFBTSxHQUFHOzs7Ozs7Ozs7Ozs7O2dCQUt2QztnQkFDQXJCLFdBQVcsQ0FBQ0MsTUFBV0M7d0JBRVpELHlCQUFBQSx3QkFBQUEsZ0JBQWlEQSwwQkFBQUEseUJBQUFBLGlCQUdqREMseUJBQUFBLHdCQUFBQSxnQkFBaURBLDBCQUFBQSx5QkFBQUE7d0JBSGpERCxtQ0FBaURBO29CQUR4RCxNQUFNYSxTQUNGLEdBQW9EYixPQUFqREEsQ0FBQUEsb0NBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcsc0NBQUFBLHlCQUFBQSxlQUFnQnpGLE9BQU8sY0FBdkJ5Riw4Q0FBQUEsMEJBQUFBLHNCQUF5QixDQUFDLEVBQUUsY0FBNUJBLDhDQUFBQSx3QkFBOEJuQyxTQUFTLGNBQXZDbUMsK0NBQUFBLG9DQUEyQyxJQUFHLEtBQStDLE9BQTVDQSxDQUFBQSxrQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyx1Q0FBQUEsMEJBQUFBLGdCQUFnQnpGLE9BQU8sY0FBdkJ5RiwrQ0FBQUEsMkJBQUFBLHVCQUF5QixDQUFDLEVBQUUsY0FBNUJBLCtDQUFBQSx5QkFBOEJsQyxPQUFPLGNBQXJDa0MsNkNBQUFBLGtDQUF5QyxPQUM3Rjt3QkFFR0MsbUNBQWlEQTtvQkFEeEQsTUFBTWEsU0FDRixHQUFvRGIsT0FBakRBLENBQUFBLG9DQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSixRQUFRLGNBQWRJLHNDQUFBQSx5QkFBQUEsZUFBZ0IxRixPQUFPLGNBQXZCMEYsOENBQUFBLDBCQUFBQSxzQkFBeUIsQ0FBQyxFQUFFLGNBQTVCQSw4Q0FBQUEsd0JBQThCcEMsU0FBUyxjQUF2Q29DLCtDQUFBQSxvQ0FBMkMsSUFBRyxLQUErQyxPQUE1Q0EsQ0FBQUEsa0NBQUFBLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU1KLFFBQVEsY0FBZEksdUNBQUFBLDBCQUFBQSxnQkFBZ0IxRixPQUFPLGNBQXZCMEYsK0NBQUFBLDJCQUFBQSx1QkFBeUIsQ0FBQyxFQUFFLGNBQTVCQSwrQ0FBQUEseUJBQThCbkMsT0FBTyxjQUFyQ21DLDZDQUFBQSxrQ0FBeUMsT0FDN0Y7b0JBQ0osT0FBT1ksT0FBT1AsYUFBYSxDQUFDUTtnQkFDaEM7WUFDSjtZQUNBO2dCQUNJckIsYUFBYTtnQkFDYmMsZUFBZTtnQkFDZmIsUUFBUTt3QkFBQyxFQUFFYyxNQUFNLEVBQW1CO3lDQUNoQyw4REFBQ3BMLG1GQUFtQkE7d0JBQUNvTCxRQUFRQTt3QkFBUXBDLE9BQU07Ozs7Ozs7Z0JBRS9DcUMsWUFBWTtnQkFDWmQsTUFBTTt3QkFBQyxFQUFFQyxHQUFHLEVBQWdCO3dCQU1aekIsa0JBRUFBLG1CQUVBQSxtQkFHSEE7b0JBWlQsTUFBTUEsV0FBV3lCLElBQUlDLFFBQVE7b0JBQzdCLHFCQUNJLDhEQUFDYzt3QkFDR0MsV0FBVzlLLG1EQUFFQSxDQUNULHdEQUNBcUksRUFBQUEsbUJBQUFBLFNBQVN2QixNQUFNLGNBQWZ1Qix1Q0FBQUEsaUJBQWlCSSxTQUFTLEtBQ3RCLHNDQUNKSixFQUFBQSxvQkFBQUEsU0FBU3ZCLE1BQU0sY0FBZnVCLHdDQUFBQSxrQkFBaUJNLGtCQUFrQixLQUMvQiw4QkFDSk4sRUFBQUEsb0JBQUFBLFNBQVN2QixNQUFNLGNBQWZ1Qix3Q0FBQUEsa0JBQWlCRyxLQUFLLE1BQUssZUFDdkI7a0NBRVBILEVBQUFBLG9CQUFBQSxTQUFTdkIsTUFBTSxjQUFmdUIsd0NBQUFBLGtCQUFpQkcsS0FBSyxLQUFJOzs7Ozs7Z0JBR3ZDO2dCQUNBeUIsV0FBVyxDQUFDQyxNQUFXQzt3QkFDREQsZ0JBQ0FDO29CQURsQixNQUFNQyxZQUFZRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSCxRQUFRLGNBQWRHLHFDQUFBQSxlQUFnQnRCLFFBQVEsS0FBSTtvQkFDOUMsTUFBTXlCLFlBQVlGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1KLFFBQVEsY0FBZEkscUNBQUFBLGVBQWdCdkIsUUFBUSxLQUFJO29CQUM5QyxPQUFPeUIsWUFBWUQ7Z0JBQ3ZCO1lBQ0o7WUFDQTtnQkFDSVQsYUFBYTtnQkFDYmMsZUFBZTtnQkFDZmIsUUFBUTt3QkFBQyxFQUFFYyxNQUFNLEVBQW1CO3lDQUNoQyw4REFBQ3BMLG1GQUFtQkE7d0JBQUNvTCxRQUFRQTt3QkFBUXBDLE9BQU07Ozs7Ozs7Z0JBRS9DcUMsWUFBWTtnQkFDWmQsTUFBTTt3QkFBQyxFQUFFQyxHQUFHLEVBQWdCO3dCQVFaekIsa0JBRUFBO29CQVRaLE1BQU1BLFdBQVd5QixJQUFJQyxRQUFRO29CQUM3QixNQUFNd0IsV0FBV3pNLFdBQVd1SixTQUFTN0MsT0FBTztvQkFFNUMscUJBQ0ksOERBQUNxRjt3QkFDR0MsV0FBVzlLLG1EQUFFQSxDQUNULGtDQUNBcUksRUFBQUEsbUJBQUFBLFNBQVN2QixNQUFNLGNBQWZ1Qix1Q0FBQUEsaUJBQWlCSSxTQUFTLEtBQ3RCLDZDQUNKSixFQUFBQSxvQkFBQUEsU0FBU3ZCLE1BQU0sY0FBZnVCLHdDQUFBQSxrQkFBaUJNLGtCQUFrQixLQUMvQjtrQ0FFUDRDOzs7Ozs7Z0JBR2I7Z0JBQ0F0QixXQUFXLENBQUNDLE1BQVdDO3dCQUVmRCxnQkFHQUM7b0JBSkosTUFBTWYsUUFBUSxJQUFJbkssS0FDZGlMLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcscUNBQUFBLGVBQWdCMUUsT0FBTyxLQUFJLEdBQzdCNkQsT0FBTztvQkFDVCxNQUFNQyxRQUFRLElBQUlySyxLQUNka0wsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUosUUFBUSxjQUFkSSxxQ0FBQUEsZUFBZ0IzRSxPQUFPLEtBQUksR0FDN0I2RCxPQUFPO29CQUNULE9BQU9DLFFBQVFGO2dCQUNuQjtZQUNKO1NBQ0g7SUFFTCxxREFBcUQ7SUFDckQsTUFBTW9DLHlCQUF5QjtRQUMzQixJQUFJOUssZUFBZThJLFFBQVEsQ0FBQyxrQkFBa0I7WUFDMUMscUVBQXFFO1lBQ3JFLE9BQU9YO1FBQ1g7UUFDQSxrRUFBa0U7UUFDbEUsT0FBT3hILHVCQUF1QixFQUFFO0lBQ3BDO0lBRUEsSUFDSSxDQUFDNkcsZUFDQSxDQUFDckosc0VBQWFBLENBQUMsaUJBQWlCcUosZ0JBQzdCLENBQUNySixzRUFBYUEsQ0FBQyxpQkFBaUJxSixnQkFDaEMsQ0FBQ3JKLHNFQUFhQSxDQUFDLG1CQUFtQnFKLGdCQUNsQyxDQUFDckosc0VBQWFBLENBQUMsd0JBQXdCcUosY0FDN0M7UUFDRSxPQUFPLENBQUNBLDRCQUNKLDhEQUFDaEosb0RBQU9BOzs7O3NDQUVSLDhEQUFDQSxvREFBT0E7WUFBQ3VNLGNBQWE7Ozs7OztJQUU5QjtJQUVBLGdDQUFnQztJQUNoQyxNQUFNQyxzQkFBc0JGO0lBQzVCLE1BQU1HLGdCQUFnQjdJLHVCQUF1QnlEO0lBRTdDLHFCQUNJLDhEQUFDc0U7UUFBSUMsV0FBVTs7MEJBQ1gsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNYLDRFQUFDcEwsaURBQUlBOzhCQUNELDRFQUFDakIsa0VBQWtCQTt3QkFDZjhCLFVBQVVBO3dCQUNWcUwsVUFBVTlGO3dCQUNWN0QsaUJBQWlCLENBQUNNO3dCQUNsQjdCLGdCQUFnQkE7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLM0JpTCw4QkFDRyw4REFBQ2Q7Z0JBQUlDLFdBQVU7O2tDQUNYLDhEQUFDdkwsb0ZBQU9BO3dCQUFDdUwsV0FBVTs7Ozs7O29CQUE4Qjs7Ozs7OzRCQUdyRFksQ0FBQUEsZ0NBQUFBLDBDQUFBQSxvQkFBcUJKLE1BQU0sSUFBRyxrQkFDOUIsOERBQUNqTSxvRUFBYUE7Z0JBQ1Z3TSxTQUFTbkM7Z0JBQ1RyRyxNQUFNcUk7Z0JBQ05JLFdBQVdyQztnQkFDWHNDLFVBQVU7Z0JBQ1ZDLGFBQWE7Ozs7OzBDQUdqQiw4REFBQ25CO2dCQUFJQyxXQUFVOzBCQUNYLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDWCw0RUFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1gsNEVBQUNtQjs0QkFBRW5CLFdBQVU7c0NBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFVcEQsQ0FBQ3BLLGVBQWU4SSxRQUFRLENBQUMsa0NBQ3RCLDhEQUFDMEM7Z0JBQ0dwQixXQUFVO2dCQUNWcUIsU0FBUyxJQUFNMUoscUJBQXFCLENBQUNFLE9BQVMsQ0FBQ0E7MEJBQzlDSixpQkFDSyxpQ0FDQTs7Ozs7Ozs7Ozs7O0FBSzFCO0dBbHNCTWpDOztRQTJCNENKLGdEQUFhQTtRQUM1Q0gsMkRBQWFBO1FBRXhCSywyRUFBaUJBO1FBMkJ5Qy9CLHlEQUFZQTtRQXNIdEU0QiwwRUFBa0JBO1FBaUJsQjVCLHlEQUFZQTs7O0tBaE1kaUM7QUFvc0JOLCtEQUFlQSxnQkFBZ0JBLEVBQUE7QUFFeEIsTUFBTThMLHNCQUFzQjtRQUFDLEVBQ2hDL0ssbUJBQW1CLEVBQ25CZ0wsZUFBZSxLQUFLLEVBQ3BCQyxpQkFBaUIsS0FBSyxFQUN0QlAsV0FBVyxFQUFFLEVBQ1g7O0lBQ0YsTUFBTTVKLFNBQVNwQywrREFBYUEsQ0FBQztJQUU3QixNQUFNd00sYUFBYXBOLHdFQUFhQSxDQUFDO1FBQzdCO1lBQ0l3SyxhQUFhO1lBQ2JDLFFBQVE7WUFDUkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNekcsT0FBWXlHLElBQUlDLFFBQVE7Z0JBQzlCLHFCQUFPLDhEQUFDMUosaUZBQWtCQTtvQkFBQ2dELE1BQU1BO29CQUFNMkcsTUFBSzs7Ozs7O1lBQ2hEO1FBQ0o7UUFDQTtZQUNJTCxhQUFhO1lBQ2JjLGVBQWU7WUFDZmIsUUFBUTtZQUNSZSxZQUFZO1lBQ1pkLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFNUGpEO2dCQUxqQixNQUFNQSxNQUFNaUQsSUFBSUMsUUFBUTtnQkFDeEIscUJBQ0k7OEJBQ0tzQyxnQkFBZ0IsdUJBQ2IsOERBQUN4Qjt3QkFBSUMsV0FBVTtrQ0FDVmpFLEVBQUFBLGNBQUFBLElBQUlsRCxNQUFNLGNBQVZrRCxrQ0FBQUEsWUFBWXlCLEtBQUssS0FBSTs7Ozs7OztZQUsxQztZQUNBMkIsV0FBVyxDQUFDQyxNQUFXQztvQkFDSkQsdUJBQUFBLGdCQUNBQyx1QkFBQUE7Z0JBRGYsTUFBTVksU0FBU2IsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyxzQ0FBQUEsd0JBQUFBLGVBQWdCdkcsTUFBTSxjQUF0QnVHLDRDQUFBQSxzQkFBd0I1QixLQUFLLEtBQUk7Z0JBQ2hELE1BQU0wQyxTQUFTYixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSixRQUFRLGNBQWRJLHNDQUFBQSx3QkFBQUEsZUFBZ0J4RyxNQUFNLGNBQXRCd0csNENBQUFBLHNCQUF3QjdCLEtBQUssS0FBSTtnQkFDaEQsT0FBT3lDLE9BQU9QLGFBQWEsQ0FBQ1E7WUFDaEM7UUFDSjtRQUNBO1lBQ0lyQixhQUFhO1lBQ2JjLGVBQWU7WUFDZmIsUUFBUTtZQUNSZSxZQUFZO1lBQ1pkLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFxQ1pqRDtnQkFwQ1osTUFBTUEsTUFBTWlELElBQUlDLFFBQVE7Z0JBQ3hCLE1BQU10RixVQUFVb0MsSUFBSXBDLE9BQU8sSUFBSSxFQUFFO2dCQUVqQyxPQUFPdEMsdUJBQ0gsOERBQUMwSTtvQkFBSUMsV0FBVTs4QkFDVnJHLFFBQVFqQixHQUFHLENBQUMsQ0FBQytEOzRCQXFCR0E7d0JBcEJiLHFCQUNJLDhEQUFDM0gsb0RBQU9BOzs4Q0FDSiw4REFBQ0UsMkRBQWNBOzhDQUNYLDRFQUFDTixtREFBTUE7d0NBQ0g0TCxNQUFLO3dDQUNMQyxTQUNJeEUsSUFBSUMsTUFBTSxDQUFDMkIsU0FBUyxHQUNkLGdCQUNBO2tEQUVWLDRFQUFDaEosMkRBQWNBOzRDQUFDcUwsV0FBVTtzREFDckJuTCxnRUFBZUEsQ0FDWjRILE9BQU9RLFNBQVMsRUFDaEJSLE9BQU9TLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzlCLDhEQUFDbkksMkRBQWNBOzt3Q0FDVjBILE9BQU9RLFNBQVM7d0NBQUU7d0NBQ2xCUixDQUFBQSxrQkFBQUEsT0FBT1MsT0FBTyxjQUFkVCw2QkFBQUEsa0JBQWtCOzs7Ozs7OzsyQkFuQmJBLE9BQU8zRCxFQUFFOzs7OztvQkF1Qi9COzs7Ozs4Q0FHSiw4REFBQ2lIO29CQUNHQyxXQUFXOUssbURBQUVBLENBQ1QsMEJBQ0E2RyxjQUFBQSxJQUFJQyxNQUFNLGNBQVZELGtDQUFBQSxZQUFZNkIsS0FBSzs4QkFFcEJqRSxRQUFRNkcsTUFBTTs7Ozs7O1lBRzNCO1lBQ0FyQixXQUFXLENBQUNDLE1BQVdDO29CQUVaRCwrQkFBQUEsOEJBQUFBLHdCQUFBQSxnQkFBd0RBLGdDQUFBQSwrQkFBQUEseUJBQUFBLGlCQUd4REMsK0JBQUFBLDhCQUFBQSx3QkFBQUEsZ0JBQXdEQSxnQ0FBQUEsK0JBQUFBLHlCQUFBQTtvQkFIeERELHlDQUF3REE7Z0JBRC9ELE1BQU1hLFNBQ0YsR0FBMkRiLE9BQXhEQSxDQUFBQSwwQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyxzQ0FBQUEseUJBQUFBLGVBQWdCekYsT0FBTyxjQUF2QnlGLDhDQUFBQSwrQkFBQUEsdUJBQXlCNUcsS0FBSyxjQUE5QjRHLG9EQUFBQSxnQ0FBQUEsNEJBQWdDLENBQUMsRUFBRSxjQUFuQ0Esb0RBQUFBLDhCQUFxQ25DLFNBQVMsY0FBOUNtQyxxREFBQUEsMENBQWtELElBQUcsS0FBc0QsT0FBbkRBLENBQUFBLHdDQUFBQSxpQkFBQUEsNEJBQUFBLGtCQUFBQSxLQUFNSCxRQUFRLGNBQWRHLHVDQUFBQSwwQkFBQUEsZ0JBQWdCekYsT0FBTyxjQUF2QnlGLCtDQUFBQSxnQ0FBQUEsd0JBQXlCNUcsS0FBSyxjQUE5QjRHLHFEQUFBQSxpQ0FBQUEsNkJBQWdDLENBQUMsRUFBRSxjQUFuQ0EscURBQUFBLCtCQUFxQ2xDLE9BQU8sY0FBNUNrQyxtREFBQUEsd0NBQWdELE9BQzNHO29CQUVHQyx5Q0FBd0RBO2dCQUQvRCxNQUFNYSxTQUNGLEdBQTJEYixPQUF4REEsQ0FBQUEsMENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1KLFFBQVEsY0FBZEksc0NBQUFBLHlCQUFBQSxlQUFnQjFGLE9BQU8sY0FBdkIwRiw4Q0FBQUEsK0JBQUFBLHVCQUF5QjdHLEtBQUssY0FBOUI2RyxvREFBQUEsZ0NBQUFBLDRCQUFnQyxDQUFDLEVBQUUsY0FBbkNBLG9EQUFBQSw4QkFBcUNwQyxTQUFTLGNBQTlDb0MscURBQUFBLDBDQUFrRCxJQUFHLEtBQXNELE9BQW5EQSxDQUFBQSx3Q0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUosUUFBUSxjQUFkSSx1Q0FBQUEsMEJBQUFBLGdCQUFnQjFGLE9BQU8sY0FBdkIwRiwrQ0FBQUEsZ0NBQUFBLHdCQUF5QjdHLEtBQUssY0FBOUI2RyxxREFBQUEsaUNBQUFBLDZCQUFnQyxDQUFDLEVBQUUsY0FBbkNBLHFEQUFBQSwrQkFBcUNuQyxPQUFPLGNBQTVDbUMsbURBQUFBLHdDQUFnRCxPQUMzRztnQkFDSixPQUFPWSxPQUFPUCxhQUFhLENBQUNRO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJckIsYUFBYTtZQUNiYyxlQUFlO1lBQ2ZiLFFBQVE7WUFDUmUsWUFBWTtZQUNaZCxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7b0JBSUZqRCxhQUF3QkEsY0FDckNBO2dCQUpULE1BQU1BLE1BQU1pRCxJQUFJQyxRQUFRO2dCQUN4QixxQkFDSSw4REFBQ2M7b0JBQ0dDLFdBQVcsR0FBa0QsT0FBL0NqRSxFQUFBQSxjQUFBQSxJQUFJQyxNQUFNLGNBQVZELGtDQUFBQSxZQUFZNEIsU0FBUyxLQUFHNUIsZUFBQUEsSUFBSUMsTUFBTSxjQUFWRCxtQ0FBQUEsYUFBWTZCLEtBQUssR0FBRyxJQUFHOzhCQUM1RDdCLEVBQUFBLGVBQUFBLElBQUlDLE1BQU0sY0FBVkQsbUNBQUFBLGFBQVkyQixLQUFLLEtBQUk7Ozs7OztZQUdsQztRQUNKO0tBQ0g7SUFFRCxxREFBcUQ7SUFDckQsTUFBTXFELFVBQVVTLGlCQUNWQyxXQUFXdEksTUFBTSxDQUFDLENBQUN1SSxNQUFhQSxJQUFJN0MsV0FBVyxLQUFLLFVBQ3BENEM7SUFFTixxQkFDSTtrQkFDS2xMLENBQUFBLGdDQUFBQSwwQ0FBQUEsb0JBQXFCaUssTUFBTSxJQUFHLGtCQUMzQiw4REFBQ2xNLGdFQUFTQTtZQUNOeU0sU0FBU0E7WUFDVHhJLE1BQU1oQztZQUNOMEssVUFBVUE7WUFDVkMsYUFBYTs7Ozs7c0NBR2pCLDhEQUFDbkI7WUFBSUMsV0FBWTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ1gsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNYLDRFQUFDbUI7d0JBQUVuQixXQUFVO2tDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVTlDLEVBQUM7SUEvSVlzQjs7UUFNTXJNLDJEQUFhQTs7O01BTm5CcU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC91aS9jcmV3LXRyYWluaW5nL2xpc3QudHN4PzRlNjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQge1xyXG4gICAgVFJBSU5JTkdfU0VTU0lPTlMsXHJcbiAgICBSRUFEX1RSQUlOSU5HX1NFU1NJT05fRFVFUyxcclxufSBmcm9tICdAL2FwcC9saWIvZ3JhcGhRTC9xdWVyeSdcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnXHJcbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcclxuaW1wb3J0IHsgVHJhaW5pbmdMaXN0RmlsdGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2ZpbHRlcidcclxuaW1wb3J0IHsgR2V0VHJhaW5pbmdTZXNzaW9uU3RhdHVzIH0gZnJvbSAnQC9hcHAvbGliL2FjdGlvbnMnXHJcbmltcG9ydCB7IGZvcm1hdCB9IGZyb20gJ2RhdGUtZm5zJ1xyXG5pbXBvcnQgeyBnZXRQZXJtaXNzaW9ucywgaGFzUGVybWlzc2lvbiB9IGZyb20gJ0AvYXBwL2hlbHBlcnMvdXNlckhlbHBlcidcclxuXHJcbi8vIEhlbHBlciBmdW5jdGlvbiB0byBmb3JtYXQgZGF0ZXMgdXNpbmcgZGF0ZS1mbnNcclxuY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlU3RyaW5nOiBhbnkpID0+IHtcclxuICAgIGlmICghZGF0ZVN0cmluZykgcmV0dXJuICcnXHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKVxyXG4gICAgICAgIHJldHVybiBmb3JtYXQoZGF0ZSwgJ2RkL01NL3l5JylcclxuICAgIH0gY2F0Y2gge1xyXG4gICAgICAgIHJldHVybiAnJ1xyXG4gICAgfVxyXG59XHJcbmltcG9ydCBMb2FkaW5nIGZyb20gJ0AvYXBwL2xvYWRpbmcnXHJcblxyXG5pbXBvcnQge1xyXG4gICAgY3JlYXRlQ29sdW1ucyxcclxuICAgIERhdGFUYWJsZSxcclxuICAgIEV4dGVuZGVkQ29sdW1uRGVmLFxyXG4gICAgRmlsdGVyZWRUYWJsZSxcclxufSBmcm9tICdAL2NvbXBvbmVudHMvZmlsdGVyZWRUYWJsZSdcclxuaW1wb3J0IHsgRGF0YVRhYmxlU29ydEhlYWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9kYXRhLXRhYmxlLXNvcnQtaGVhZGVyJ1xyXG5pbXBvcnQgeyBMb2FkZXIyIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xyXG5pbXBvcnQge1xyXG4gICAgQXZhdGFyLFxyXG4gICAgQXZhdGFyRmFsbGJhY2ssXHJcbiAgICBDYXJkLFxyXG4gICAgZ2V0Q3Jld0luaXRpYWxzLFxyXG4gICAgVG9vbHRpcCxcclxuICAgIFRvb2x0aXBDb250ZW50LFxyXG4gICAgVG9vbHRpcFRyaWdnZXIsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpJ1xyXG5pbXBvcnQgeyB1c2VNZWRpYVF1ZXJ5IH0gZnJvbSAnQHJlYWN0dXNlcy9jb3JlJ1xyXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvYXBwL2xpYi91dGlscydcclxuaW1wb3J0IHsgdXNlVHJhaW5pbmdGaWx0ZXJzIH0gZnJvbSAnLi9ob29rcy91c2VUcmFpbmluZ0ZpbHRlcnMnXHJcbmltcG9ydCB7IHVzZVF1ZXJ5U3RhdGUgfSBmcm9tICdudXFzJ1xyXG5pbXBvcnQgeyBMb2NhdGlvbk1vZGFsIH0gZnJvbSAnLi4vdmVzc2Vscy9saXN0J1xyXG5pbXBvcnQgeyB1c2VWZXNzZWxJY29uRGF0YSB9IGZyb20gJ0AvYXBwL2xpYi92ZXNzZWwtaWNvbi1oZWxwZXInXHJcblxyXG5pbXBvcnQgeyBNb2JpbGVUcmFpbmluZ0NhcmQgfSBmcm9tICcuL2NvbXBvbmVudHMvbW9iaWxlLXRyYWluaW5nLWNhcmQnXHJcblxyXG5jb25zdCBDcmV3VHJhaW5pbmdMaXN0ID0gKHtcclxuICAgIG1lbWJlcklkID0gMCxcclxuICAgIHZlc3NlbElkID0gMCxcclxuICAgIGFwcGx5RmlsdGVyUmVmLFxyXG4gICAgZXhjbHVkZUZpbHRlcnMgPSBbXSxcclxufToge1xyXG4gICAgbWVtYmVySWQ/OiBudW1iZXJcclxuICAgIHZlc3NlbElkPzogbnVtYmVyXHJcbiAgICBhcHBseUZpbHRlclJlZj86IGFueVxyXG4gICAgZXhjbHVkZUZpbHRlcnM/OiBzdHJpbmdbXVxyXG59KSA9PiB7XHJcbiAgICBjb25zdCBsaW1pdCA9IDEwMFxyXG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXHJcbiAgICBjb25zdCBbcGFnZUluZm8sIHNldFBhZ2VJbmZvXSA9IHVzZVN0YXRlKHtcclxuICAgICAgICB0b3RhbENvdW50OiAwLFxyXG4gICAgICAgIGhhc05leHRQYWdlOiBmYWxzZSxcclxuICAgICAgICBoYXNQcmV2aW91c1BhZ2U6IGZhbHNlLFxyXG4gICAgfSlcclxuICAgIGNvbnN0IFt0cmFpbmluZ0xpc3QsIHNldFRyYWluaW5nTGlzdF0gPSB1c2VTdGF0ZShbXSBhcyBhbnkpXHJcbiAgICBjb25zdCBbdHJhaW5pbmdTZXNzaW9uRHVlcywgc2V0VHJhaW5pbmdTZXNzaW9uRHVlc10gPSB1c2VTdGF0ZShbXSBhcyBhbnkpXHJcbiAgICBjb25zdCBbcGFnZSwgc2V0UGFnZV0gPSB1c2VTdGF0ZSgwKVxyXG4gICAgY29uc3QgW3Zlc3NlbElkT3B0aW9ucywgc2V0VmVzc2VsSWRPcHRpb25zXSA9IHVzZVN0YXRlKFtdIGFzIGFueSlcclxuICAgIGNvbnN0IFt0cmFpbmluZ1R5cGVJZE9wdGlvbnMsIHNldFRyYWluaW5nVHlwZUlkT3B0aW9uc10gPSB1c2VTdGF0ZShcclxuICAgICAgICBbXSBhcyBhbnksXHJcbiAgICApXHJcbiAgICBjb25zdCBbdHJhaW5lcklkT3B0aW9ucywgc2V0VHJhaW5lcklkT3B0aW9uc10gPSB1c2VTdGF0ZShbXSBhcyBhbnkpXHJcbiAgICBjb25zdCBbY3Jld0lkT3B0aW9ucywgc2V0Q3Jld0lkT3B0aW9uc10gPSB1c2VTdGF0ZShbXSBhcyBhbnkpXHJcbiAgICBjb25zdCBbb3ZlcmR1ZVN3aXRjaGVyLCBzZXRPdmVyZHVlU3dpdGNoZXJdID0gdXNlUXVlcnlTdGF0ZSgnb3ZlcmR1ZScpXHJcbiAgICBjb25zdCBpc1dpZGUgPSB1c2VNZWRpYVF1ZXJ5KCcobWluLXdpZHRoOiA3MjBweCknKVxyXG4gICAgY29uc3QgeyBnZXRWZXNzZWxXaXRoSWNvbiwgbG9hZGluZzogdmVzc2VsRGF0YUxvYWRpbmcgfSA9XHJcbiAgICAgICAgdXNlVmVzc2VsSWNvbkRhdGEoKVxyXG5cclxuICAgIC8vIENyZWF0ZSBhIGJvb2xlYW4gc3RhdGUgd3JhcHBlciBmb3IgdGhlIHVzZVRyYWluaW5nRmlsdGVycyBob29rXHJcbiAgICBjb25zdCBbb3ZlcmR1ZUJvb2xlYW4sIHNldE92ZXJkdWVCb29sZWFuXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKVxyXG5cclxuICAgIC8vIFN5bmMgdGhlIGJvb2xlYW4gc3RhdGUgd2l0aCB0aGUgcXVlcnkgc3RhdGVcclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgc2V0T3ZlcmR1ZUJvb2xlYW4ob3ZlcmR1ZVN3aXRjaGVyID09PSAndHJ1ZScpXHJcbiAgICB9LCBbb3ZlcmR1ZVN3aXRjaGVyXSlcclxuXHJcbiAgICAvLyBDcmVhdGUgYSB3cmFwcGVyIGZ1bmN0aW9uIHRoYXQgY29udmVydHMgYm9vbGVhbiB0byBzdHJpbmcgZm9yIHRoZSBxdWVyeSBzdGF0ZVxyXG4gICAgY29uc3QgdG9nZ2xlT3ZlcmR1ZVdyYXBwZXIgPSB1c2VDYWxsYmFjayhcclxuICAgICAgICAodmFsdWU6IGJvb2xlYW4gfCAoKHByZXY6IGJvb2xlYW4pID0+IGJvb2xlYW4pKSA9PiB7XHJcbiAgICAgICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbicpIHtcclxuICAgICAgICAgICAgICAgIHNldE92ZXJkdWVCb29sZWFuKChwcmV2KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3VmFsdWUgPSB2YWx1ZShwcmV2KVxyXG4gICAgICAgICAgICAgICAgICAgIHNldE92ZXJkdWVTd2l0Y2hlcihuZXdWYWx1ZSA/ICd0cnVlJyA6ICdmYWxzZScpXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ld1ZhbHVlXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgc2V0T3ZlcmR1ZUJvb2xlYW4odmFsdWUpXHJcbiAgICAgICAgICAgICAgICBzZXRPdmVyZHVlU3dpdGNoZXIodmFsdWUgPyAndHJ1ZScgOiAnZmFsc2UnKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBbc2V0T3ZlcmR1ZVN3aXRjaGVyXSxcclxuICAgIClcclxuXHJcbiAgICBjb25zdCBbcXVlcnlUcmFpbmluZ0xpc3QsIHsgbG9hZGluZzogdHJhaW5pbmdMaXN0TG9hZGluZyB9XSA9IHVzZUxhenlRdWVyeShcclxuICAgICAgICBUUkFJTklOR19TRVNTSU9OUyxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgICAgICAgICAgICAgICfwn5SNIFJhdyB0cmFpbmluZyBzZXNzaW9ucyBkYXRhIGZyb20gQVBJOicsXHJcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UucmVhZFRyYWluaW5nU2Vzc2lvbnMsXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZFRyYWluaW5nU2Vzc2lvbnMubm9kZXNcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBUcmFuc2Zvcm0gdmVzc2VsIGRhdGEgdG8gaW5jbHVkZSBjb21wbGV0ZSB2ZXNzZWwgaW5mb3JtYXRpb24gd2l0aCBwb3NpdGlvblxyXG4gICAgICAgICAgICAgICAgY29uc3QgdHJhbnNmb3JtZWREYXRhID0gZGF0YS5tYXAoKGl0ZW06IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbXBsZXRlVmVzc2VsRGF0YSA9IGdldFZlc3NlbFdpdGhJY29uKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpdGVtLnZlc3NlbC5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS52ZXNzZWwsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5pdGVtLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw6IGNvbXBsZXRlVmVzc2VsRGF0YSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgICAgICAgICAgICAgICfwn5SEIFRyYW5zZm9ybWVkIHRyYWluaW5nIHNlc3Npb25zIGRhdGE6JyxcclxuICAgICAgICAgICAgICAgICAgICB0cmFuc2Zvcm1lZERhdGEsXHJcbiAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgY29uc3QgdmVzc2VsSURzID0gQXJyYXkuZnJvbShcclxuICAgICAgICAgICAgICAgICAgICBuZXcgU2V0KGRhdGEubWFwKChpdGVtOiBhbnkpID0+IGl0ZW0udmVzc2VsLmlkKSksXHJcbiAgICAgICAgICAgICAgICApLmZpbHRlcigoaWQ6IGFueSkgPT4gK2lkICE9PSAwKVxyXG4gICAgICAgICAgICAgICAgY29uc3QgdHJhaW5pbmdUeXBlSURzID0gQXJyYXkuZnJvbShcclxuICAgICAgICAgICAgICAgICAgICBuZXcgU2V0KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRhLmZsYXRNYXAoKGl0ZW06IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0udHJhaW5pbmdUeXBlcy5ub2Rlcy5tYXAoKHQ6IGFueSkgPT4gdC5pZCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIGNvbnN0IHRyYWluZXJJRHMgPSBBcnJheS5mcm9tKFxyXG4gICAgICAgICAgICAgICAgICAgIG5ldyBTZXQoZGF0YS5tYXAoKGl0ZW06IGFueSkgPT4gaXRlbS50cmFpbmVySUQpKSxcclxuICAgICAgICAgICAgICAgICkuZmlsdGVyKChpZDogYW55KSA9PiAraWQgIT09IDApXHJcbiAgICAgICAgICAgICAgICBjb25zdCBtZW1iZXJJRHMgPSBBcnJheS5mcm9tKFxyXG4gICAgICAgICAgICAgICAgICAgIG5ldyBTZXQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEuZmxhdE1hcCgoaXRlbTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5tZW1iZXJzLm5vZGVzLm1hcCgodDogYW55KSA9PiB0LmlkKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgIGlmICh0cmFuc2Zvcm1lZERhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICBzZXRUcmFpbmluZ0xpc3QodHJhbnNmb3JtZWREYXRhKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldFZlc3NlbElkT3B0aW9ucyh2ZXNzZWxJRHMpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdUeXBlSWRPcHRpb25zKHRyYWluaW5nVHlwZUlEcylcclxuICAgICAgICAgICAgICAgICAgICBzZXRUcmFpbmVySWRPcHRpb25zKHRyYWluZXJJRHMpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3Jld0lkT3B0aW9ucyhtZW1iZXJJRHMpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBzZXRQYWdlSW5mbyhyZXNwb25zZS5yZWFkVHJhaW5pbmdTZXNzaW9ucy5wYWdlSW5mbylcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5VHJhaW5pbmdMaXN0IGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgIClcclxuXHJcbiAgICBjb25zdCBsb2FkVHJhaW5pbmdMaXN0ID0gYXN5bmMgKFxyXG4gICAgICAgIHN0YXJ0UGFnZTogbnVtYmVyID0gMCxcclxuICAgICAgICBzZWFyY2hGaWx0ZXI6IGFueSA9IHsgLi4uZmlsdGVyIH0sXHJcbiAgICApID0+IHtcclxuICAgICAgICBhd2FpdCBxdWVyeVRyYWluaW5nTGlzdCh7XHJcbiAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgZmlsdGVyOiBzZWFyY2hGaWx0ZXIsXHJcbiAgICAgICAgICAgICAgICBvZmZzZXQ6IHN0YXJ0UGFnZSAqIGxpbWl0LFxyXG4gICAgICAgICAgICAgICAgbGltaXQ6IGxpbWl0LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMgPSBhc3luYyAoZmlsdGVyOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zdCBkdWVGaWx0ZXI6IGFueSA9IHt9XHJcbiAgICAgICAgaWYgKG1lbWJlcklkID4gMCkge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIubWVtYmVySUQgPSB7IGVxOiArbWVtYmVySWQgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodmVzc2VsSWQgPiAwKSB7XHJcbiAgICAgICAgICAgIGR1ZUZpbHRlci52ZXNzZWxJRCA9IHsgZXE6ICt2ZXNzZWxJZCB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChmaWx0ZXIudmVzc2VsSUQpIHtcclxuICAgICAgICAgICAgZHVlRmlsdGVyLnZlc3NlbElEID0gZmlsdGVyLnZlc3NlbElEXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChmaWx0ZXIudHJhaW5pbmdUeXBlcykge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIudHJhaW5pbmdUeXBlSUQgPSB7IGVxOiBmaWx0ZXIudHJhaW5pbmdUeXBlcy5pZC5jb250YWlucyB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChmaWx0ZXIubWVtYmVycykge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIubWVtYmVySUQgPSB7IGVxOiBmaWx0ZXIubWVtYmVycy5pZC5jb250YWlucyB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChmaWx0ZXIuZGF0ZSkge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIuZHVlRGF0ZSA9IGZpbHRlci5kYXRlXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgZHVlRmlsdGVyLmR1ZURhdGUgPSB7IG5lOiBudWxsIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgYXdhaXQgcmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMoe1xyXG4gICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgIGZpbHRlcjogZHVlRmlsdGVyLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlTmF2aWdhdGlvbkNsaWNrID0gKG5ld1BhZ2U6IGFueSkgPT4ge1xyXG4gICAgICAgIGlmIChuZXdQYWdlIDwgMCB8fCBuZXdQYWdlID09PSBwYWdlKSByZXR1cm5cclxuICAgICAgICBzZXRQYWdlKG5ld1BhZ2UpXHJcbiAgICAgICAgbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMoZmlsdGVyKVxyXG4gICAgICAgIGxvYWRUcmFpbmluZ0xpc3QobmV3UGFnZSwgZmlsdGVyKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHtcclxuICAgICAgICBmaWx0ZXIsXHJcbiAgICAgICAgc2V0RmlsdGVyLCAvLyB1c2VkIGluIHRoZSBzdGFydHVwIGVmZmVjdFxyXG4gICAgICAgIGhhbmRsZUZpbHRlckNoYW5nZSwgLy8gcmVwbGFjZXMgdGhlIG9sZCBoYW5kbGVGaWx0ZXJPbkNoYW5nZVxyXG4gICAgfSA9IHVzZVRyYWluaW5nRmlsdGVycyh7XHJcbiAgICAgICAgaW5pdGlhbEZpbHRlcjoge30sXHJcbiAgICAgICAgbG9hZExpc3Q6IGxvYWRUcmFpbmluZ0xpc3QsXHJcbiAgICAgICAgbG9hZER1ZXM6IGxvYWRUcmFpbmluZ1Nlc3Npb25EdWVzLFxyXG4gICAgICAgIHRvZ2dsZU92ZXJkdWU6IHRvZ2dsZU92ZXJkdWVXcmFwcGVyLFxyXG4gICAgfSlcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChhcHBseUZpbHRlclJlZilcclxuICAgICAgICAgICAgYXBwbHlGaWx0ZXJSZWYuY3VycmVudCA9IHtcclxuICAgICAgICAgICAgICAgIGFwcGx5OiBoYW5kbGVGaWx0ZXJDaGFuZ2UsIC8vIHRyaWdnZXIgaXRcclxuICAgICAgICAgICAgICAgIG92ZXJkdWU6IG92ZXJkdWVCb29sZWFuLCAvLyByZWFkIHNuYXBzaG90XHJcbiAgICAgICAgICAgICAgICBzZXRPdmVyZHVlOiB0b2dnbGVPdmVyZHVlV3JhcHBlciwgLy8gdG9nZ2xlIGRpcmVjdGx5IGlmIHlvdSB3YW50XHJcbiAgICAgICAgICAgIH1cclxuICAgIH0sIFtoYW5kbGVGaWx0ZXJDaGFuZ2UsIG92ZXJkdWVCb29sZWFuLCB0b2dnbGVPdmVyZHVlV3JhcHBlcl0pXHJcblxyXG4gICAgY29uc3QgW3JlYWRUcmFpbmluZ1Nlc3Npb25EdWVzLCB7IGxvYWRpbmc6IHRyYWluaW5nU2Vzc2lvbkR1ZXNMb2FkaW5nIH1dID1cclxuICAgICAgICB1c2VMYXp5UXVlcnkoUkVBRF9UUkFJTklOR19TRVNTSU9OX0RVRVMsIHtcclxuICAgICAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgICAgICAgJ/CflI0gUmF3IHRyYWluaW5nIHNlc3Npb24gZHVlcyBkYXRhIGZyb20gQVBJOicsXHJcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UucmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMsXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMubm9kZXNcclxuICAgICAgICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gRmlsdGVyIG91dCBjcmV3IG1lbWJlcnMgd2hvIGFyZSBubyBsb25nZXIgYXNzaWduZWQgdG8gdGhlIHZlc3NlbC5cclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWx0ZXJlZERhdGEgPSBkYXRhLmZpbHRlcigoaXRlbTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpdGVtLnZlc3NlbC5zZWFMb2dzTWVtYmVycy5ub2Rlcy5zb21lKChtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBtLmlkID09PSBpdGVtLm1lbWJlcklEXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBkdWVXaXRoU3RhdHVzID0gZmlsdGVyZWREYXRhLm1hcCgoZHVlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uZHVlLCBzdGF0dXM6IEdldFRyYWluaW5nU2Vzc2lvblN0YXR1cyhkdWUpIH1cclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIC8vIFJldHVybiBvbmx5IGR1ZSB3aXRoaW4gNyBkYXlzIGFuZCBvdmVyZHVlXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gY29uc3QgZmlsdGVyZWREdWVXaXRoU3RhdHVzID0gZHVlV2l0aFN0YXR1cy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gICAgIChpdGVtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAvLyAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gICAgICAgICAgICAgaXRlbS5zdGF0dXMuaXNPdmVyZHVlIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gICAgICAgICAgICAgKGl0ZW0uc3RhdHVzLmlzT3ZlcmR1ZSA9PT0gZmFsc2UgJiZcclxuICAgICAgICAgICAgICAgICAgICAvLyAgICAgICAgICAgICAgICAgaXRlbS5zdGF0dXMuZHVlV2l0aGluU2V2ZW5EYXlzID09PSB0cnVlKVxyXG4gICAgICAgICAgICAgICAgICAgIC8vICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIC8vICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIC8vIClcclxuICAgICAgICAgICAgICAgICAgICAvLyBjb25zdCBncm91cGVkRHVlcyA9IGZpbHRlcmVkRHVlV2l0aFN0YXR1cy5yZWR1Y2UoXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZ3JvdXBlZER1ZXMgPSBkdWVXaXRoU3RhdHVzLnJlZHVjZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgKGFjYzogYW55LCBkdWU6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qga2V5ID0gYCR7ZHVlLnZlc3NlbElEfS0ke2R1ZS50cmFpbmluZ1R5cGVJRH0tJHtkdWUuZHVlRGF0ZX1gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWFjY1trZXldKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjW2tleV0gPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBkdWUuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbElEOiBkdWUudmVzc2VsSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbDogZHVlLnZlc3NlbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IGR1ZS50cmFpbmluZ1R5cGVJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiBkdWUudHJhaW5pbmdUeXBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdWVEYXRlOiBkdWUuZHVlRGF0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBkdWUuc3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ0xvY2F0aW9uVHlwZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1ZS50cmFpbmluZ1Nlc3Npb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAudHJhaW5pbmdMb2NhdGlvblR5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcnM6IFtdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjY1trZXldLm1lbWJlcnMucHVzaChkdWUubWVtYmVyKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFjY1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7fSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG1lcmdlZER1ZXMgPSBPYmplY3QudmFsdWVzKGdyb3VwZWREdWVzKS5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChncm91cDogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXJnZWRNZW1iZXJzID0gZ3JvdXAubWVtYmVycy5yZWR1Y2UoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGFjYzogYW55LCBtZW1iZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZ01lbWJlciA9IGFjYy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKG06IGFueSkgPT4gbS5pZCA9PT0gbWVtYmVyLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChleGlzdGluZ01lbWJlcikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIuZmlyc3ROYW1lID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuZmlyc3ROYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ01lbWJlci5zdXJuYW1lID0gbWVtYmVyLnN1cm5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjYy5wdXNoKG1lbWJlcilcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gYWNjXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IGdyb3VwLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbElEOiBncm91cC52ZXNzZWxJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw6IGdyb3VwLnZlc3NlbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1R5cGVJRDogZ3JvdXAudHJhaW5pbmdUeXBlSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiBncm91cC50cmFpbmluZ1R5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBncm91cC5zdGF0dXMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVlRGF0ZTogZ3JvdXAuZHVlRGF0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ0xvY2F0aW9uVHlwZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ3JvdXAudHJhaW5pbmdMb2NhdGlvblR5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyczogbWVyZ2VkTWVtYmVycyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICfwn5SEIFByb2Nlc3NlZCB0cmFpbmluZyBzZXNzaW9uIGR1ZXM6JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWVyZ2VkRHVlcyxcclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdTZXNzaW9uRHVlcyhtZXJnZWREdWVzKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigncmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMgZXJyb3InLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKGlzTG9hZGluZykge1xyXG4gICAgICAgICAgICBjb25zdCBmOiB7IG1lbWJlcnM/OiBhbnkgfSA9IHsgLi4uZmlsdGVyIH1cclxuICAgICAgICAgICAgaWYgKCttZW1iZXJJZCA+IDApIHtcclxuICAgICAgICAgICAgICAgIGYubWVtYmVycyA9IHsgaWQ6IHsgY29udGFpbnM6ICttZW1iZXJJZCB9IH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBzZXRGaWx0ZXIoZilcclxuICAgICAgICAgICAgbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMoZilcclxuICAgICAgICAgICAgbG9hZFRyYWluaW5nTGlzdCgwLCBmKVxyXG4gICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW2lzTG9hZGluZ10pXHJcblxyXG4gICAgY29uc3QgW3Blcm1pc3Npb25zLCBzZXRQZXJtaXNzaW9uc10gPSB1c2VTdGF0ZTxhbnk+KGZhbHNlKVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgc2V0UGVybWlzc2lvbnMoZ2V0UGVybWlzc2lvbnMpXHJcbiAgICB9LCBbXSlcclxuXHJcbiAgICAvLyBUcmFuc2Zvcm0gY29tcGxldGVkIHRyYWluaW5nIHNlc3Npb25zIHRvIG1hdGNoIHVuaWZpZWQgc3RydWN0dXJlXHJcbiAgICBjb25zdCB0cmFuc2Zvcm1Db21wbGV0ZWRUcmFpbmluZ3MgPSAodHJhaW5pbmdMaXN0OiBhbnlbXSkgPT4ge1xyXG4gICAgICAgIHJldHVybiB0cmFpbmluZ0xpc3QubWFwKCh0cmFpbmluZzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIC8vIEVuc3VyZSB2ZXNzZWwgaGFzIGNvbXBsZXRlIGRhdGEgaW5jbHVkaW5nIHBvc2l0aW9uXHJcbiAgICAgICAgICAgIGNvbnN0IGNvbXBsZXRlVmVzc2VsRGF0YSA9IGdldFZlc3NlbFdpdGhJY29uKFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmcudmVzc2VsPy5pZCxcclxuICAgICAgICAgICAgICAgIHRyYWluaW5nLnZlc3NlbCxcclxuICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgIGlkOiB0cmFpbmluZy5pZCxcclxuICAgICAgICAgICAgICAgIGR1ZURhdGU6IHRyYWluaW5nLmRhdGUsIC8vIE1hcCBkYXRlIHRvIGR1ZURhdGUgZm9yIGNvbnNpc3RlbmN5XHJcbiAgICAgICAgICAgICAgICB2ZXNzZWxJRDogdHJhaW5pbmcudmVzc2VsPy5pZCxcclxuICAgICAgICAgICAgICAgIHZlc3NlbDogY29tcGxldGVWZXNzZWxEYXRhLFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IHRyYWluaW5nLnRyYWluaW5nVHlwZXM/Lm5vZGVzPy5bMF0/LmlkLFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiB0cmFpbmluZy50cmFpbmluZ1R5cGVzPy5ub2Rlcz8uWzBdIHx8IHtcclxuICAgICAgICAgICAgICAgICAgICB0aXRsZTogJycsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgbWVtYmVyczogdHJhaW5pbmcubWVtYmVycz8ubm9kZXMgfHwgW10sXHJcbiAgICAgICAgICAgICAgICB0cmFpbmVyOiB0cmFpbmluZy50cmFpbmVyLCAvLyBLZWVwIHRyYWluZXIgaW5mbyBmb3IgY29tcGxldGVkIHRyYWluaW5nc1xyXG4gICAgICAgICAgICAgICAgc3RhdHVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICdDb21wbGV0ZWQnLFxyXG4gICAgICAgICAgICAgICAgICAgIGlzT3ZlcmR1ZTogZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3M6ICdib3JkZXIgcm91bmRlZCBib3JkZXItYm9yZGVyIHRleHQtaW5wdXQgYmctb3V0ZXItc3BhY2UtNTAgcC0yIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlcicsXHJcbiAgICAgICAgICAgICAgICAgICAgZHVlV2l0aGluU2V2ZW5EYXlzOiBmYWxzZSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB0cmFpbmluZ0xvY2F0aW9uVHlwZTogdHJhaW5pbmcudHJhaW5pbmdMb2NhdGlvblR5cGUsXHJcbiAgICAgICAgICAgICAgICAvLyBBZGQgcHJpb3JpdHkgZm9yIHNvcnRpbmcgKGhpZ2hlciBudW1iZXIgPSBoaWdoZXIgcHJpb3JpdHkpXHJcbiAgICAgICAgICAgICAgICBwcmlvcml0eTogMCwgLy8gQ29tcGxldGVkIHRyYWluaW5ncyBoYXZlIGxvd2VzdCBwcmlvcml0eVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICAvLyBDcmVhdGUgdW5pZmllZCBhbmQgc29ydGVkIHRyYWluaW5nIGRhdGFcclxuICAgIGNvbnN0IGNyZWF0ZVVuaWZpZWRUcmFpbmluZ0RhdGEgPSAoKSA9PiB7XHJcbiAgICAgICAgLy8gVHJhbnNmb3JtIGNvbXBsZXRlZCB0cmFpbmluZ3NcclxuICAgICAgICBjb25zdCB0cmFuc2Zvcm1lZENvbXBsZXRlZCA9IHRyYW5zZm9ybUNvbXBsZXRlZFRyYWluaW5ncyhcclxuICAgICAgICAgICAgdHJhaW5pbmdMaXN0IHx8IFtdLFxyXG4gICAgICAgIClcclxuXHJcbiAgICAgICAgLy8gQWRkIHByaW9yaXR5IHRvIG92ZXJkdWUvdXBjb21pbmcgdHJhaW5pbmdzXHJcbiAgICAgICAgY29uc3QgcHJpb3JpdGl6ZWREdWVzID0gKHRyYWluaW5nU2Vzc2lvbkR1ZXMgfHwgW10pLm1hcCgoZHVlOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgIC4uLmR1ZSxcclxuICAgICAgICAgICAgcHJpb3JpdHk6IGR1ZS5zdGF0dXM/LmlzT3ZlcmR1ZVxyXG4gICAgICAgICAgICAgICAgPyAzXHJcbiAgICAgICAgICAgICAgICA6IGR1ZS5zdGF0dXM/LmR1ZVdpdGhpblNldmVuRGF5c1xyXG4gICAgICAgICAgICAgICAgICA/IDJcclxuICAgICAgICAgICAgICAgICAgOiAxLFxyXG4gICAgICAgIH0pKVxyXG5cclxuICAgICAgICAvLyBDb21iaW5lIGFsbCB0cmFpbmluZyBkYXRhXHJcbiAgICAgICAgY29uc3QgYWxsVHJhaW5pbmdzID0gWy4uLnByaW9yaXRpemVkRHVlcywgLi4udHJhbnNmb3JtZWRDb21wbGV0ZWRdXHJcblxyXG4gICAgICAgIC8vIFNvcnQgYnkgcHJpb3JpdHkgKGRlc2NlbmRpbmcpLCB0aGVuIGJ5IGR1ZSBkYXRlIChhc2NlbmRpbmcgZm9yIG92ZXJkdWUvdXBjb21pbmcsIGRlc2NlbmRpbmcgZm9yIGNvbXBsZXRlZClcclxuICAgICAgICByZXR1cm4gYWxsVHJhaW5pbmdzLnNvcnQoKGE6IGFueSwgYjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIC8vIEZpcnN0IHNvcnQgYnkgcHJpb3JpdHkgKG92ZXJkdWUgPiB1cGNvbWluZyA+IG90aGVyID4gY29tcGxldGVkKVxyXG4gICAgICAgICAgICBpZiAoYS5wcmlvcml0eSAhPT0gYi5wcmlvcml0eSkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGIucHJpb3JpdHkgLSBhLnByaW9yaXR5XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIFdpdGhpbiBzYW1lIHByaW9yaXR5LCBzb3J0IGJ5IGRhdGVcclxuICAgICAgICAgICAgY29uc3QgZGF0ZUEgPSBuZXcgRGF0ZShhLmR1ZURhdGUgfHwgMCkuZ2V0VGltZSgpXHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGVCID0gbmV3IERhdGUoYi5kdWVEYXRlIHx8IDApLmdldFRpbWUoKVxyXG5cclxuICAgICAgICAgICAgLy8gRm9yIG92ZXJkdWUgYW5kIHVwY29taW5nIChwcmlvcml0eSA+IDApLCBzb3J0IGJ5IGR1ZSBkYXRlIGFzY2VuZGluZyAoZWFybGllc3QgZmlyc3QpXHJcbiAgICAgICAgICAgIGlmIChhLnByaW9yaXR5ID4gMCkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGRhdGVBIC0gZGF0ZUJcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gRm9yIGNvbXBsZXRlZCB0cmFpbmluZ3MgKHByaW9yaXR5ID0gMCksIHNvcnQgYnkgZGF0ZSBkZXNjZW5kaW5nIChtb3N0IHJlY2VudCBmaXJzdClcclxuICAgICAgICAgICAgcmV0dXJuIGRhdGVCIC0gZGF0ZUFcclxuICAgICAgICB9KVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENvbWJpbmVkIGxvYWRpbmcgc3RhdGUgZm9yIHVuaWZpZWQgdmlld1xyXG4gICAgY29uc3QgaXNVbmlmaWVkRGF0YUxvYWRpbmcgPSBleGNsdWRlRmlsdGVycy5pbmNsdWRlcygnb3ZlcmR1ZVRvZ2dsZScpXHJcbiAgICAgICAgPyB0cmFpbmluZ0xpc3RMb2FkaW5nIHx8IHRyYWluaW5nU2Vzc2lvbkR1ZXNMb2FkaW5nXHJcbiAgICAgICAgOiBmYWxzZVxyXG5cclxuICAgIC8vIFJvdyBzdGF0dXMgZXZhbHVhdGlvbiBmb3IgRmlsdGVyZWRUYWJsZSBzdHlsaW5nXHJcbiAgICBjb25zdCBnZXRUcmFpbmluZ1Jvd1N0YXR1cyA9IChcclxuICAgICAgICB0cmFpbmluZzogYW55LFxyXG4gICAgKTogJ292ZXJkdWUnIHwgJ3VwY29taW5nJyB8ICdub3JtYWwnID0+IHtcclxuICAgICAgICBpZiAoIXRyYWluaW5nLnN0YXR1cykgcmV0dXJuICdub3JtYWwnXHJcblxyXG4gICAgICAgIGlmICh0cmFpbmluZy5zdGF0dXMuaXNPdmVyZHVlKSB7XHJcbiAgICAgICAgICAgIHJldHVybiAnb3ZlcmR1ZSdcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmICh0cmFpbmluZy5zdGF0dXMuZHVlV2l0aGluU2V2ZW5EYXlzKSB7XHJcbiAgICAgICAgICAgIHJldHVybiAndXBjb21pbmcnXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICByZXR1cm4gJ25vcm1hbCdcclxuICAgIH1cclxuXHJcbiAgICAvLyBVbmlmaWVkIGNvbHVtbiBkZWZpbml0aW9ucyBmb3IgdGhlIHRyYWluaW5nIHRhYmxlXHJcbiAgICBjb25zdCBjcmVhdGVVbmlmaWVkVHJhaW5pbmdDb2x1bW5zID0gKCkgPT5cclxuICAgICAgICBjcmVhdGVDb2x1bW5zKFtcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICd0aXRsZScsXHJcbiAgICAgICAgICAgICAgICBoZWFkZXI6ICdUcmFpbmluZycsXHJcbiAgICAgICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdHJhaW5pbmc6IGFueSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxNb2JpbGVUcmFpbmluZ0NhcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE9e3RyYWluaW5nfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySWQ9e21lbWJlcklkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcuc3RhdHVzPy5sYWJlbCA9PT0gJ0NvbXBsZXRlZCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnY29tcGxldGVkJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdvdmVyZHVlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIFNvcnQgYnkgcHJpb3JpdHkgZmlyc3QsIHRoZW4gYnkgdHJhaW5pbmcgdHlwZSBuYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJpb3JpdHlBID0gcm93QT8ub3JpZ2luYWw/LnByaW9yaXR5IHx8IDBcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcmlvcml0eUIgPSByb3dCPy5vcmlnaW5hbD8ucHJpb3JpdHkgfHwgMFxyXG5cclxuICAgICAgICAgICAgICAgICAgICBpZiAocHJpb3JpdHlBICE9PSBwcmlvcml0eUIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHByaW9yaXR5QiAtIHByaW9yaXR5QVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmFtZUEgPSByb3dBPy5vcmlnaW5hbD8udHJhaW5pbmdUeXBlPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5hbWVCID0gcm93Qj8ub3JpZ2luYWw/LnRyYWluaW5nVHlwZT8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbmFtZUEubG9jYWxlQ29tcGFyZShuYW1lQilcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndHJhaW5pbmdUeXBlJyxcclxuICAgICAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyxcclxuICAgICAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbHVtbj17Y29sdW1ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIlRyYWluaW5nIFR5cGVcIlxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgYnJlYWtwb2ludDogJ3RhYmxldC1tZCcsXHJcbiAgICAgICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdHJhaW5pbmcgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB0cmFpbmluZ1R5cGUgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZy50cmFpbmluZ1R5cGU/LnRpdGxlIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnRyYWluaW5nVHlwZXM/Lm5vZGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/Lm1hcCgodDogYW55KSA9PiB0LnRpdGxlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmpvaW4oJywgJykgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgJydcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1zbSBmb250LW1lZGl1bScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcuc3RhdHVzPy5pc092ZXJkdWUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtY2lubmFiYXItNTAwIGhvdmVyOnRleHQtY2lubmFiYXItNzAwJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZy5zdGF0dXM/LmR1ZVdpdGhpblNldmVuRGF5cyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1maXJlLWJ1c2gtNjAwIGhvdmVyOnRleHQtZmlyZS1idXNoLTcwMCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0cmFpbmluZ1R5cGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IHJvd0E/Lm9yaWdpbmFsPy50cmFpbmluZ1R5cGU/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID0gcm93Qj8ub3JpZ2luYWw/LnRyYWluaW5nVHlwZT8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICd2ZXNzZWwnLFxyXG4gICAgICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnLFxyXG4gICAgICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiVmVzc2VsXCIgLz5cclxuICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICBicmVha3BvaW50OiAnbGFuZHNjYXBlJyxcclxuICAgICAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB0cmFpbmluZyA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ub3dyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dHJhaW5pbmcudmVzc2VsPy50aXRsZSB8fCAnJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMb2NhdGlvbk1vZGFsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsPXt0cmFpbmluZy52ZXNzZWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbkNsYXNzTmFtZT1cInNpemUtOFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPSByb3dBPy5vcmlnaW5hbD8udmVzc2VsPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy52ZXNzZWw/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnY3JldycsXHJcbiAgICAgICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnY2VudGVyJyxcclxuICAgICAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIkNyZXdcIiAvPlxyXG4gICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICdsYXB0b3AnLFxyXG4gICAgICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRyYWluaW5nID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWVtYmVycyA9IHRyYWluaW5nLm1lbWJlcnMgfHwgW11cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0xIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWVtYmVycy5zbGljZSgwLCA1KS5tYXAoKG1lbWJlcjogYW55KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAga2V5PXttZW1iZXIuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcuc3RhdHVzPy5pc092ZXJkdWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2Rlc3RydWN0aXZlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB0cmFpbmluZy5zdGF0dXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPy5kdWVXaXRoaW5TZXZlbkRheXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnd2FybmluZydcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnc2Vjb25kYXJ5J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrIGNsYXNzTmFtZT1cInRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENyZXdJbml0aWFscyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci5maXJzdE5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuc3VybmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW1iZXIuZmlyc3ROYW1lfXsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWVtYmVyLnN1cm5hbWUgPz8gJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWVtYmVycy5sZW5ndGggPiA1ICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1sLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgK3ttZW1iZXJzLmxlbmd0aCAtIDV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBgJHtyb3dBPy5vcmlnaW5hbD8ubWVtYmVycz8uWzBdPy5maXJzdE5hbWUgPz8gJyd9ICR7cm93QT8ub3JpZ2luYWw/Lm1lbWJlcnM/LlswXT8uc3VybmFtZSA/PyAnJ31gIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICcnXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgYCR7cm93Qj8ub3JpZ2luYWw/Lm1lbWJlcnM/LlswXT8uZmlyc3ROYW1lID8/ICcnfSAke3Jvd0I/Lm9yaWdpbmFsPy5tZW1iZXJzPy5bMF0/LnN1cm5hbWUgPz8gJyd9YCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZUEubG9jYWxlQ29tcGFyZSh2YWx1ZUIpXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBhY2Nlc3NvcktleTogJ3N0YXR1cycsXHJcbiAgICAgICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnY2VudGVyJyxcclxuICAgICAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIlN0YXR1c1wiIC8+XHJcbiAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgYnJlYWtwb2ludDogJ2xhbmRzY2FwZScsXHJcbiAgICAgICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdHJhaW5pbmcgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdweC0yIHB5LTEgcm91bmRlZC1tZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtY2VudGVyJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZy5zdGF0dXM/LmlzT3ZlcmR1ZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnYmctZGVzdHJ1Y3RpdmUvMTAgdGV4dC1kZXN0cnVjdGl2ZScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcuc3RhdHVzPy5kdWVXaXRoaW5TZXZlbkRheXMgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLXdhcm5pbmcvMTAgdGV4dC13YXJuaW5nJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZy5zdGF0dXM/LmxhYmVsID09PSAnQ29tcGxldGVkJyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnYmctbXV0ZWQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3RyYWluaW5nLnN0YXR1cz8ubGFiZWwgfHwgJ1Vua25vd24nfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcmlvcml0eUEgPSByb3dBPy5vcmlnaW5hbD8ucHJpb3JpdHkgfHwgMFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHByaW9yaXR5QiA9IHJvd0I/Lm9yaWdpbmFsPy5wcmlvcml0eSB8fCAwXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHByaW9yaXR5QiAtIHByaW9yaXR5QVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdkdWVEYXRlJyxcclxuICAgICAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdyaWdodCcsXHJcbiAgICAgICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJEYXRlXCIgLz5cclxuICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICBicmVha3BvaW50OiAndGFibGV0LW1kJyxcclxuICAgICAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB0cmFpbmluZyA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGRhdGVUZXh0ID0gZm9ybWF0RGF0ZSh0cmFpbmluZy5kdWVEYXRlKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcmlnaHQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnN0YXR1cz8uaXNPdmVyZHVlICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LWNpbm5hYmFyLTUwMCBob3Zlcjp0ZXh0LWNpbm5hYmFyLTcwMCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcuc3RhdHVzPy5kdWVXaXRoaW5TZXZlbkRheXMgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtZmlyZS1idXNoLTYwMCBob3Zlcjp0ZXh0LWZpcmUtYnVzaC03MDAnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGF0ZVRleHR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGRhdGVBID0gbmV3IERhdGUoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvd0E/Lm9yaWdpbmFsPy5kdWVEYXRlIHx8IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgKS5nZXRUaW1lKClcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRlQiA9IG5ldyBEYXRlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICByb3dCPy5vcmlnaW5hbD8uZHVlRGF0ZSB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgICkuZ2V0VGltZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGVCIC0gZGF0ZUFcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgXSlcclxuXHJcbiAgICAvLyBHZXQgdW5pZmllZCB0cmFpbmluZyBkYXRhIGJhc2VkIG9uIGZpbHRlciBzZXR0aW5nc1xyXG4gICAgY29uc3QgZ2V0VW5pZmllZFRyYWluaW5nRGF0YSA9ICgpID0+IHtcclxuICAgICAgICBpZiAoZXhjbHVkZUZpbHRlcnMuaW5jbHVkZXMoJ292ZXJkdWVUb2dnbGUnKSkge1xyXG4gICAgICAgICAgICAvLyBVc2UgdGhlIG5ldyB1bmlmaWVkIGFuZCBzb3J0ZWQgZGF0YSB3aGVuIG92ZXJkdWVUb2dnbGUgaXMgZXhjbHVkZWRcclxuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVVuaWZpZWRUcmFpbmluZ0RhdGEoKVxyXG4gICAgICAgIH1cclxuICAgICAgICAvLyBSZXR1cm4gb25seSBvdmVyZHVlL3VwY29taW5nIHRyYWluaW5ncyB3aGVuIHRvZ2dsZSBpcyBhdmFpbGFibGVcclxuICAgICAgICByZXR1cm4gdHJhaW5pbmdTZXNzaW9uRHVlcyB8fCBbXVxyXG4gICAgfVxyXG5cclxuICAgIGlmIChcclxuICAgICAgICAhcGVybWlzc2lvbnMgfHxcclxuICAgICAgICAoIWhhc1Blcm1pc3Npb24oJ0VESVRfVFJBSU5JTkcnLCBwZXJtaXNzaW9ucykgJiZcclxuICAgICAgICAgICAgIWhhc1Blcm1pc3Npb24oJ1ZJRVdfVFJBSU5JTkcnLCBwZXJtaXNzaW9ucykgJiZcclxuICAgICAgICAgICAgIWhhc1Blcm1pc3Npb24oJ1JFQ09SRF9UUkFJTklORycsIHBlcm1pc3Npb25zKSAmJlxyXG4gICAgICAgICAgICAhaGFzUGVybWlzc2lvbignVklFV19NRU1CRVJfVFJBSU5JTkcnLCBwZXJtaXNzaW9ucykpXHJcbiAgICApIHtcclxuICAgICAgICByZXR1cm4gIXBlcm1pc3Npb25zID8gKFxyXG4gICAgICAgICAgICA8TG9hZGluZyAvPlxyXG4gICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgIDxMb2FkaW5nIGVycm9yTWVzc2FnZT1cIk9vcHMgWW91IGRvIG5vdCBoYXZlIHRoZSBwZXJtaXNzaW9uIHRvIHZpZXcgdGhpcyBzZWN0aW9uLlwiIC8+XHJcbiAgICAgICAgKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIEdldCB0aGUgdW5pZmllZCB0cmFpbmluZyBkYXRhXHJcbiAgICBjb25zdCB1bmlmaWVkVHJhaW5pbmdEYXRhID0gZ2V0VW5pZmllZFRyYWluaW5nRGF0YSgpXHJcbiAgICBjb25zdCBpc0RhdGFMb2FkaW5nID0gdHJhaW5pbmdMaXN0TG9hZGluZyB8fCB0cmFpbmluZ1Nlc3Npb25EdWVzTG9hZGluZ1xyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi01XCI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgICA8VHJhaW5pbmdMaXN0RmlsdGVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklkPXttZW1iZXJJZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUZpbHRlckNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb3ZlcmR1ZVN3aXRjaGVyPXshb3ZlcmR1ZUJvb2xlYW59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4Y2x1ZGVGaWx0ZXJzPXtleGNsdWRlRmlsdGVyc31cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIHtpc0RhdGFMb2FkaW5nID8gKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTggdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00IGFuaW1hdGUtc3BpblwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgTG9hZGluZyB0cmFpbmluZyBkYXRhLi4uXHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSA6IHVuaWZpZWRUcmFpbmluZ0RhdGE/Lmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgICA8RmlsdGVyZWRUYWJsZVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbHVtbnM9e2NyZWF0ZVVuaWZpZWRUcmFpbmluZ0NvbHVtbnMoKX1cclxuICAgICAgICAgICAgICAgICAgICBkYXRhPXt1bmlmaWVkVHJhaW5pbmdEYXRhfVxyXG4gICAgICAgICAgICAgICAgICAgIHJvd1N0YXR1cz17Z2V0VHJhaW5pbmdSb3dTdGF0dXN9XHJcbiAgICAgICAgICAgICAgICAgICAgcGFnZVNpemU9ezIwfVxyXG4gICAgICAgICAgICAgICAgICAgIHNob3dUb29sYmFyPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyb3VwIGJvcmRlci1iXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgY29sLXNwYW4tNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBnYXAtMiBwLTIgcHQtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgV09XISBMb29rIGF0IHRoYXQuIEFsbCB5b3VyIGNyZXcgYXJlIHNoaXAtc2hhcGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYW5kIHRyYWluZWQgdG8gdGhlIGdpbGxzLiBHcmVhdCBqb2IsIGNhcHRhaW4hXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICB7LyogT3B0aW9uYWwgdG9nZ2xlIGJ1dHRvbiBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eSAqL31cclxuICAgICAgICAgICAgeyFleGNsdWRlRmlsdGVycy5pbmNsdWRlcygnb3ZlcmR1ZVRvZ2dsZScpICYmIChcclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC00IGl0ZW1zLWNlbnRlciByb3VuZGVkLWxnIGdhcC00IHhzOmdhcC0wIGJnLWJhY2tncm91bmQgYm9yZGVyIGJvcmRlci1ib3JkZXIgcC01IHRleHQtY2VudGVyIGhvdmVyOnRleHQtbGlnaHQtYmx1ZS12aXZpZC05MDAgdy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB0b2dnbGVPdmVyZHVlV3JhcHBlcigocHJldikgPT4gIXByZXYpfT5cclxuICAgICAgICAgICAgICAgICAgICB7b3ZlcmR1ZUJvb2xlYW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnVmlldyBhbGwgY29tcGxldGVkIHRyYWluaW5ncydcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiAnVmlldyBvdmVyZHVlIHRyYWluaW5ncyd9XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgIClcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ3Jld1RyYWluaW5nTGlzdFxyXG5cclxuZXhwb3J0IGNvbnN0IE92ZXJkdWVUcmFpbmluZ0xpc3QgPSAoe1xyXG4gICAgdHJhaW5pbmdTZXNzaW9uRHVlcyxcclxuICAgIGlzVmVzc2VsVmlldyA9IGZhbHNlLFxyXG4gICAgaGlkZUNyZXdDb2x1bW4gPSBmYWxzZSxcclxuICAgIHBhZ2VTaXplID0gMjAsXHJcbn06IGFueSkgPT4ge1xyXG4gICAgY29uc3QgaXNXaWRlID0gdXNlTWVkaWFRdWVyeSgnKG1pbi13aWR0aDogNzIwcHgpJylcclxuXHJcbiAgICBjb25zdCBhbGxDb2x1bW5zID0gY3JlYXRlQ29sdW1ucyhbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3RpdGxlJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAnVHJhaW5pbmcnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhOiBhbnkgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiA8TW9iaWxlVHJhaW5pbmdDYXJkIGRhdGE9e2RhdGF9IHR5cGU9XCJvdmVyZHVlXCIgLz5cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICd2ZXNzZWwnLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnbGVmdCcsXHJcbiAgICAgICAgICAgIGhlYWRlcjogJ1Zlc3NlbCcsXHJcbiAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICdsYW5kc2NhcGUnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkdWUgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2lzVmVzc2VsVmlldyA9PSBmYWxzZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDp0YWJsZS1jZWxsIHAtMiBhbGlnbi10b3AgbGc6YWxpZ24tbWlkZGxlIGl0ZW1zLWNlbnRlciB0ZXh0LWxlZnRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZHVlLnZlc3NlbD8udGl0bGUgfHwgJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IHJvd0E/Lm9yaWdpbmFsPy52ZXNzZWw/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSByb3dCPy5vcmlnaW5hbD8udmVzc2VsPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdjcmV3JyxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ3JpZ2h0JyxcclxuICAgICAgICAgICAgaGVhZGVyOiAnQ3JldycsXHJcbiAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICdsYXB0b3AnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkdWUgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIGNvbnN0IG1lbWJlcnMgPSBkdWUubWVtYmVycyB8fCBbXVxyXG5cclxuICAgICAgICAgICAgICAgIHJldHVybiBpc1dpZGUgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHttZW1iZXJzLm1hcCgobWVtYmVyOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAga2V5PXttZW1iZXIuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVlLnN0YXR1cy5pc092ZXJkdWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2Rlc3RydWN0aXZlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnc2Vjb25kYXJ5J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrIGNsYXNzTmFtZT1cInRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENyZXdJbml0aWFscyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci5maXJzdE5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuc3VybmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW1iZXIuZmlyc3ROYW1lfXsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWVtYmVyLnN1cm5hbWUgPz8gJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJyFyb3VuZGVkLWZ1bGwgc2l6ZS0xMCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdWUuc3RhdHVzPy5jbGFzcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHttZW1iZXJzLmxlbmd0aH1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9XHJcbiAgICAgICAgICAgICAgICAgICAgYCR7cm93QT8ub3JpZ2luYWw/Lm1lbWJlcnM/Lm5vZGVzPy5bMF0/LmZpcnN0TmFtZSA/PyAnJ30gJHtyb3dBPy5vcmlnaW5hbD8ubWVtYmVycz8ubm9kZXM/LlswXT8uc3VybmFtZSA/PyAnJ31gIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgJydcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9XHJcbiAgICAgICAgICAgICAgICAgICAgYCR7cm93Qj8ub3JpZ2luYWw/Lm1lbWJlcnM/Lm5vZGVzPy5bMF0/LmZpcnN0TmFtZSA/PyAnJ30gJHtyb3dCPy5vcmlnaW5hbD8ubWVtYmVycz8ubm9kZXM/LlswXT8uc3VybmFtZSA/PyAnJ31gIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgJydcclxuICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZUEubG9jYWxlQ29tcGFyZSh2YWx1ZUIpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnc3RhdHVzJyxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ3JpZ2h0JyxcclxuICAgICAgICAgICAgaGVhZGVyOiAnU3RhdHVzJyxcclxuICAgICAgICAgICAgYnJlYWtwb2ludDogJ2xhbmRzY2FwZScsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGR1ZSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YCR7ZHVlLnN0YXR1cz8uaXNPdmVyZHVlID8gZHVlLnN0YXR1cz8uY2xhc3MgOiAnJ30gcm91bmRlZC1tZCB3LWZpdCAhcC0yIHRleHQtbm93cmFwYH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtkdWUuc3RhdHVzPy5sYWJlbCB8fCAnVW5rbm93biBTdGF0dXMnfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICBdKVxyXG5cclxuICAgIC8vIEZpbHRlciBvdXQgY3JldyBjb2x1bW4gd2hlbiBoaWRlQ3Jld0NvbHVtbiBpcyB0cnVlXHJcbiAgICBjb25zdCBjb2x1bW5zID0gaGlkZUNyZXdDb2x1bW5cclxuICAgICAgICA/IGFsbENvbHVtbnMuZmlsdGVyKChjb2w6IGFueSkgPT4gY29sLmFjY2Vzc29yS2V5ICE9PSAnY3JldycpXHJcbiAgICAgICAgOiBhbGxDb2x1bW5zXHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8PlxyXG4gICAgICAgICAgICB7dHJhaW5pbmdTZXNzaW9uRHVlcz8ubGVuZ3RoID4gMCA/IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVcclxuICAgICAgICAgICAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxyXG4gICAgICAgICAgICAgICAgICAgIGRhdGE9e3RyYWluaW5nU2Vzc2lvbkR1ZXN9XHJcbiAgICAgICAgICAgICAgICAgICAgcGFnZVNpemU9e3BhZ2VTaXplfVxyXG4gICAgICAgICAgICAgICAgICAgIHNob3dUb29sYmFyPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGdyb3VwIGJvcmRlci1iIGhvdmVyOiBgfT5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBjb2wtc3Bhbi00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGdhcC0yIHAtMiBwdC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCIgIFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFdPVyEgTG9vayBhdCB0aGF0LiBBbGwgeW91ciBjcmV3IGFyZSBzaGlwLXNoYXBlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFuZCB0cmFpbmVkIHRvIHRoZSBnaWxscy4gR3JlYXQgam9iLCBjYXB0YWluIVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgIDwvPlxyXG4gICAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJUUkFJTklOR19TRVNTSU9OUyIsIlJFQURfVFJBSU5JTkdfU0VTU0lPTl9EVUVTIiwidXNlTGF6eVF1ZXJ5IiwidXNlQ2FsbGJhY2siLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIlRyYWluaW5nTGlzdEZpbHRlciIsIkdldFRyYWluaW5nU2Vzc2lvblN0YXR1cyIsImZvcm1hdCIsImdldFBlcm1pc3Npb25zIiwiaGFzUGVybWlzc2lvbiIsImZvcm1hdERhdGUiLCJkYXRlU3RyaW5nIiwiZGF0ZSIsIkRhdGUiLCJMb2FkaW5nIiwiY3JlYXRlQ29sdW1ucyIsIkRhdGFUYWJsZSIsIkZpbHRlcmVkVGFibGUiLCJEYXRhVGFibGVTb3J0SGVhZGVyIiwiTG9hZGVyMiIsIkF2YXRhciIsIkF2YXRhckZhbGxiYWNrIiwiQ2FyZCIsImdldENyZXdJbml0aWFscyIsIlRvb2x0aXAiLCJUb29sdGlwQ29udGVudCIsIlRvb2x0aXBUcmlnZ2VyIiwidXNlTWVkaWFRdWVyeSIsImNuIiwidXNlVHJhaW5pbmdGaWx0ZXJzIiwidXNlUXVlcnlTdGF0ZSIsIkxvY2F0aW9uTW9kYWwiLCJ1c2VWZXNzZWxJY29uRGF0YSIsIk1vYmlsZVRyYWluaW5nQ2FyZCIsIkNyZXdUcmFpbmluZ0xpc3QiLCJtZW1iZXJJZCIsInZlc3NlbElkIiwiYXBwbHlGaWx0ZXJSZWYiLCJleGNsdWRlRmlsdGVycyIsImxpbWl0IiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwicGFnZUluZm8iLCJzZXRQYWdlSW5mbyIsInRvdGFsQ291bnQiLCJoYXNOZXh0UGFnZSIsImhhc1ByZXZpb3VzUGFnZSIsInRyYWluaW5nTGlzdCIsInNldFRyYWluaW5nTGlzdCIsInRyYWluaW5nU2Vzc2lvbkR1ZXMiLCJzZXRUcmFpbmluZ1Nlc3Npb25EdWVzIiwicGFnZSIsInNldFBhZ2UiLCJ2ZXNzZWxJZE9wdGlvbnMiLCJzZXRWZXNzZWxJZE9wdGlvbnMiLCJ0cmFpbmluZ1R5cGVJZE9wdGlvbnMiLCJzZXRUcmFpbmluZ1R5cGVJZE9wdGlvbnMiLCJ0cmFpbmVySWRPcHRpb25zIiwic2V0VHJhaW5lcklkT3B0aW9ucyIsImNyZXdJZE9wdGlvbnMiLCJzZXRDcmV3SWRPcHRpb25zIiwib3ZlcmR1ZVN3aXRjaGVyIiwic2V0T3ZlcmR1ZVN3aXRjaGVyIiwiaXNXaWRlIiwiZ2V0VmVzc2VsV2l0aEljb24iLCJsb2FkaW5nIiwidmVzc2VsRGF0YUxvYWRpbmciLCJvdmVyZHVlQm9vbGVhbiIsInNldE92ZXJkdWVCb29sZWFuIiwidG9nZ2xlT3ZlcmR1ZVdyYXBwZXIiLCJ2YWx1ZSIsInByZXYiLCJuZXdWYWx1ZSIsInF1ZXJ5VHJhaW5pbmdMaXN0IiwidHJhaW5pbmdMaXN0TG9hZGluZyIsImZldGNoUG9saWN5Iiwib25Db21wbGV0ZWQiLCJyZXNwb25zZSIsImNvbnNvbGUiLCJsb2ciLCJyZWFkVHJhaW5pbmdTZXNzaW9ucyIsImRhdGEiLCJub2RlcyIsInRyYW5zZm9ybWVkRGF0YSIsIm1hcCIsIml0ZW0iLCJjb21wbGV0ZVZlc3NlbERhdGEiLCJ2ZXNzZWwiLCJpZCIsInZlc3NlbElEcyIsIkFycmF5IiwiZnJvbSIsIlNldCIsImZpbHRlciIsInRyYWluaW5nVHlwZUlEcyIsImZsYXRNYXAiLCJ0cmFpbmluZ1R5cGVzIiwidCIsInRyYWluZXJJRHMiLCJ0cmFpbmVySUQiLCJtZW1iZXJJRHMiLCJtZW1iZXJzIiwib25FcnJvciIsImVycm9yIiwibG9hZFRyYWluaW5nTGlzdCIsInN0YXJ0UGFnZSIsInNlYXJjaEZpbHRlciIsInZhcmlhYmxlcyIsIm9mZnNldCIsImxvYWRUcmFpbmluZ1Nlc3Npb25EdWVzIiwiZHVlRmlsdGVyIiwibWVtYmVySUQiLCJlcSIsInZlc3NlbElEIiwidHJhaW5pbmdUeXBlSUQiLCJjb250YWlucyIsImR1ZURhdGUiLCJuZSIsInJlYWRUcmFpbmluZ1Nlc3Npb25EdWVzIiwiaGFuZGxlTmF2aWdhdGlvbkNsaWNrIiwibmV3UGFnZSIsInNldEZpbHRlciIsImhhbmRsZUZpbHRlckNoYW5nZSIsImluaXRpYWxGaWx0ZXIiLCJsb2FkTGlzdCIsImxvYWREdWVzIiwidG9nZ2xlT3ZlcmR1ZSIsImN1cnJlbnQiLCJhcHBseSIsIm92ZXJkdWUiLCJzZXRPdmVyZHVlIiwidHJhaW5pbmdTZXNzaW9uRHVlc0xvYWRpbmciLCJmaWx0ZXJlZERhdGEiLCJzZWFMb2dzTWVtYmVycyIsInNvbWUiLCJtIiwiZHVlV2l0aFN0YXR1cyIsImR1ZSIsInN0YXR1cyIsImdyb3VwZWREdWVzIiwicmVkdWNlIiwiYWNjIiwia2V5IiwidHJhaW5pbmdUeXBlIiwidHJhaW5pbmdMb2NhdGlvblR5cGUiLCJ0cmFpbmluZ1Nlc3Npb24iLCJwdXNoIiwibWVtYmVyIiwibWVyZ2VkRHVlcyIsIk9iamVjdCIsInZhbHVlcyIsImdyb3VwIiwibWVyZ2VkTWVtYmVycyIsImV4aXN0aW5nTWVtYmVyIiwiZmluZCIsImZpcnN0TmFtZSIsInN1cm5hbWUiLCJmIiwicGVybWlzc2lvbnMiLCJzZXRQZXJtaXNzaW9ucyIsInRyYW5zZm9ybUNvbXBsZXRlZFRyYWluaW5ncyIsInRyYWluaW5nIiwidGl0bGUiLCJ0cmFpbmVyIiwibGFiZWwiLCJpc092ZXJkdWUiLCJjbGFzcyIsImR1ZVdpdGhpblNldmVuRGF5cyIsInByaW9yaXR5IiwiY3JlYXRlVW5pZmllZFRyYWluaW5nRGF0YSIsInRyYW5zZm9ybWVkQ29tcGxldGVkIiwicHJpb3JpdGl6ZWREdWVzIiwiYWxsVHJhaW5pbmdzIiwic29ydCIsImEiLCJiIiwiZGF0ZUEiLCJnZXRUaW1lIiwiZGF0ZUIiLCJpc1VuaWZpZWREYXRhTG9hZGluZyIsImluY2x1ZGVzIiwiZ2V0VHJhaW5pbmdSb3dTdGF0dXMiLCJjcmVhdGVVbmlmaWVkVHJhaW5pbmdDb2x1bW5zIiwiYWNjZXNzb3JLZXkiLCJoZWFkZXIiLCJjZWxsIiwicm93Iiwib3JpZ2luYWwiLCJ0eXBlIiwic29ydGluZ0ZuIiwicm93QSIsInJvd0IiLCJwcmlvcml0eUEiLCJwcmlvcml0eUIiLCJuYW1lQSIsIm5hbWVCIiwibG9jYWxlQ29tcGFyZSIsImNlbGxBbGlnbm1lbnQiLCJjb2x1bW4iLCJicmVha3BvaW50Iiwiam9pbiIsImRpdiIsImNsYXNzTmFtZSIsInZhbHVlQSIsInZhbHVlQiIsInNwYW4iLCJpY29uQ2xhc3NOYW1lIiwic2xpY2UiLCJzaXplIiwidmFyaWFudCIsImxlbmd0aCIsImRhdGVUZXh0IiwiZ2V0VW5pZmllZFRyYWluaW5nRGF0YSIsImVycm9yTWVzc2FnZSIsInVuaWZpZWRUcmFpbmluZ0RhdGEiLCJpc0RhdGFMb2FkaW5nIiwib25DaGFuZ2UiLCJjb2x1bW5zIiwicm93U3RhdHVzIiwicGFnZVNpemUiLCJzaG93VG9vbGJhciIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwiT3ZlcmR1ZVRyYWluaW5nTGlzdCIsImlzVmVzc2VsVmlldyIsImhpZGVDcmV3Q29sdW1uIiwiYWxsQ29sdW1ucyIsImNvbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});