"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/crew-training/crew-training-client.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _list__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./list */ \"(app-pages-browser)/./src/app/ui/crew-training/list.tsx\");\n/* harmony import */ var _components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter/components/training-actions */ \"(app-pages-browser)/./src/components/filter/components/training-actions.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/icons/SealogsTrainingIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsTrainingIcon.ts\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CrewTrainingClient = ()=>{\n    _s();\n    const applyFilterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /** ⬅️ 1) reactive state that drives the heading */ const [isOverdueEnabled, setIsOverdueEnabled] = (0,nuqs__WEBPACK_IMPORTED_MODULE_6__.useQueryState)(\"overdue\", nuqs__WEBPACK_IMPORTED_MODULE_6__.parseAsBoolean.withDefault(false));\n    /** ⬅️ 2) keep both the list and the heading in sync */ const handleDropdownChange = (type, data)=>{\n        var _applyFilterRef_current;\n        if (type === \"overdue\") setIsOverdueEnabled(!!data);\n        (_applyFilterRef_current = applyFilterRef.current) === null || _applyFilterRef_current === void 0 ? void 0 : _applyFilterRef_current.apply({\n            type,\n            data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"block w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_5__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_4__.SealogsTrainingIcon, {\n                    className: \"h-12 w-12 ring-1 p-0.5 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 21\n                }, void 0),\n                title: \"\".concat(!isOverdueEnabled ? \"Overdue and upcoming\" : \"Completed\", \" trainings\"),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_3__.CrewTrainingFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"overdue\", data);\n                    },\n                    overdueList: isOverdueEnabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_list__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    applyFilterRef: applyFilterRef,\n                    excludeFilters: [\n                        \"overdueToggle\"\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 52,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n        lineNumber: 33,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingClient, \"bS3Kzy3/ZN9ewOASDeuRAmhItXY=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_6__.useQueryState\n    ];\n});\n_c = CrewTrainingClient;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingClient);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx\n"));

/***/ })

});