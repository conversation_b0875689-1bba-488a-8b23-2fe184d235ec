"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // const [filter, setFilter] = useState({})\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isVesselView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overdueSwitcher, setOverdueSwitcher] = (0,nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState)(\"overdue\");\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    // Create a boolean state wrapper for the useTrainingFilters hook\n    const [overdueBoolean, setOverdueBoolean] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Sync the boolean state with the query state\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setOverdueBoolean(overdueSwitcher === \"true\");\n    }, [\n        overdueSwitcher\n    ]);\n    // Create a wrapper function that converts boolean to string for the query state\n    const toggleOverdueWrapper = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        if (typeof value === \"function\") {\n            setOverdueBoolean((prev)=>{\n                const newValue = value(prev);\n                setOverdueSwitcher(newValue ? \"true\" : \"false\");\n                return newValue;\n            });\n        } else {\n            setOverdueBoolean(value);\n            setOverdueSwitcher(value ? \"true\" : \"false\");\n        }\n    }, [\n        setOverdueSwitcher\n    ]);\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            if (transformedData) {\n                setTrainingList(transformedData);\n                setVesselIdOptions(vesselIDs);\n                setTrainingTypeIdOptions(trainingTypeIDs);\n                setTrainerIdOptions(trainerIDs);\n                setCrewIdOptions(memberIDs);\n            }\n            setPageInfo(response.readTrainingSessions.pageInfo);\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadTrainingSessionDues(filter);\n        loadTrainingList(newPage, filter);\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: toggleOverdueWrapper\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: overdueBoolean,\n            setOverdue: toggleOverdueWrapper\n        };\n    }, [\n        handleFilterChange,\n        overdueBoolean,\n        toggleOverdueWrapper\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match unified structure\n    const transformCompletedTrainings = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                trainer: training.trainer,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                trainingLocationType: training.trainingLocationType,\n                // Add priority for sorting (higher number = higher priority)\n                priority: 0\n            };\n        });\n    };\n    // Create unified and sorted training data\n    const createUnifiedTrainingData = ()=>{\n        // Transform completed trainings\n        const transformedCompleted = transformCompletedTrainings(trainingList || []);\n        // Add priority to overdue/upcoming trainings\n        const prioritizedDues = (trainingSessionDues || []).map((due)=>{\n            var _due_status, _due_status1;\n            return {\n                ...due,\n                priority: ((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? 3 : ((_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.dueWithinSevenDays) ? 2 : 1\n            };\n        });\n        // Combine all training data\n        const allTrainings = [\n            ...prioritizedDues,\n            ...transformedCompleted\n        ];\n        // Sort by priority (descending), then by due date (ascending for overdue/upcoming, descending for completed)\n        return allTrainings.sort((a, b)=>{\n            // First sort by priority (overdue > upcoming > other > completed)\n            if (a.priority !== b.priority) {\n                return b.priority - a.priority;\n            }\n            // Within same priority, sort by date\n            const dateA = new Date(a.dueDate || 0).getTime();\n            const dateB = new Date(b.dueDate || 0).getTime();\n            // For overdue and upcoming (priority > 0), sort by due date ascending (earliest first)\n            if (a.priority > 0) {\n                return dateA - dateB;\n            }\n            // For completed trainings (priority = 0), sort by date descending (most recent first)\n            return dateB - dateA;\n        });\n    };\n    // Combined loading state for unified view\n    const isUnifiedDataLoading = excludeFilters.includes(\"overdueToggle\") ? trainingListLoading || trainingSessionDuesLoading : false;\n    // Row status evaluation for FilteredTable styling\n    const getTrainingRowStatus = (training)=>{\n        if (!training.status) return \"normal\";\n        if (training.status.isOverdue) {\n            return \"overdue\";\n        }\n        if (training.status.dueWithinSevenDays) {\n            return \"upcoming\";\n        }\n        return \"normal\";\n    };\n    // Unified column definitions for the training table\n    const createUnifiedTrainingColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: \"Training\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                        data: training,\n                        memberId: memberId,\n                        type: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.label) === \"Completed\" ? \"completed\" : \"overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_trainingType, _rowB_original1;\n                    // Sort by priority first, then by training type name\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    if (priorityA !== priorityB) {\n                        return priorityB - priorityA;\n                    }\n                    const nameA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const nameB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return nameA.localeCompare(nameB);\n                }\n            },\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_trainingType, _training_trainingTypes_nodes, _training_trainingTypes, _training_status, _training_status1;\n                    const training = row.original;\n                    const trainingType = ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : _training_trainingTypes_nodes.map((t)=>t.title).join(\", \")) || \"\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-sm font-medium\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: trainingType\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_trainingType, _rowA_original, _rowB_original_trainingType, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_14__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Crew\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    const members = training.members || [];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 justify-center\",\n                        children: [\n                            members.slice(0, 5).map((member)=>/*#__PURE__*/ {\n                                var _training_status, _training_status1;\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                                size: \"sm\",\n                                                variant: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? \"destructive\" : ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) ? \"warning\" : \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 33\n                                }, undefined);\n                            }),\n                            members.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground ml-1\",\n                                children: [\n                                    \"+\",\n                                    members.length - 5\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 554,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_members_, _rowA_original_members, _rowA_original, _rowA_original_members_1, _rowA_original_members1, _rowA_original1, _rowB_original_members_, _rowB_original_members, _rowB_original, _rowB_original_members_1, _rowB_original_members1, _rowB_original1;\n                    var _rowA_original_members__firstName, _rowA_original_members__surname;\n                    const valueA = \"\".concat((_rowA_original_members__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_ = _rowA_original_members[0]) === null || _rowA_original_members_ === void 0 ? void 0 : _rowA_original_members_.firstName) !== null && _rowA_original_members__firstName !== void 0 ? _rowA_original_members__firstName : \"\", \" \").concat((_rowA_original_members__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_1 = _rowA_original_members1[0]) === null || _rowA_original_members_1 === void 0 ? void 0 : _rowA_original_members_1.surname) !== null && _rowA_original_members__surname !== void 0 ? _rowA_original_members__surname : \"\") || \"\";\n                    var _rowB_original_members__firstName, _rowB_original_members__surname;\n                    const valueB = \"\".concat((_rowB_original_members__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_ = _rowB_original_members[0]) === null || _rowB_original_members_ === void 0 ? void 0 : _rowB_original_members_.firstName) !== null && _rowB_original_members__firstName !== void 0 ? _rowB_original_members__firstName : \"\", \" \").concat((_rowB_original_members__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_1 = _rowB_original_members1[0]) === null || _rowB_original_members_1 === void 0 ? void 0 : _rowB_original_members_1.surname) !== null && _rowB_original_members__surname !== void 0 ? _rowB_original_members__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1, _training_status2, _training_status3;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded-md text-xs font-medium text-center\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"bg-destructive/10 text-destructive\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"bg-warning/10 text-warning\", ((_training_status2 = training.status) === null || _training_status2 === void 0 ? void 0 : _training_status2.label) === \"Completed\" && \"bg-muted text-muted-foreground\"),\n                        children: ((_training_status3 = training.status) === null || _training_status3 === void 0 ? void 0 : _training_status3.label) || \"Unknown\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    return priorityB - priorityA;\n                }\n            },\n            {\n                accessorKey: \"dueDate\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1;\n                    const training = row.original;\n                    const dateText = formatDate(training.dueDate);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-sm font-medium text-right\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: dateText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || 0).getTime();\n                    const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || 0).getTime();\n                    return dateB - dateA;\n                }\n            }\n        ]);\n    // Get unified training data based on filter settings\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            // Use the new unified and sorted data when overdueToggle is excluded\n            return createUnifiedTrainingData();\n        }\n        // Return only overdue/upcoming trainings when toggle is available\n        return trainingSessionDues || [];\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 684,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 686,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Get the unified training data\n    const unifiedTrainingData = getUnifiedTrainingData();\n    const isDataLoading = trainingListLoading || trainingSessionDuesLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: !overdueBoolean,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 697,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 696,\n                columnNumber: 13\n            }, undefined),\n            isDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 709,\n                        columnNumber: 21\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 708,\n                columnNumber: 17\n            }, undefined) : (unifiedTrainingData === null || unifiedTrainingData === void 0 ? void 0 : unifiedTrainingData.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.FilteredTable, {\n                columns: createUnifiedTrainingColumns(),\n                data: unifiedTrainingData,\n                rowStatus: getTrainingRowStatus,\n                pageSize: 20,\n                showToolbar: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 713,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 col-span-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 722,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 721,\n                columnNumber: 17\n            }, undefined),\n            !excludeFilters.includes(\"overdueToggle\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full\",\n                onClick: ()=>toggleOverdueWrapper((prev)=>!prev),\n                children: overdueBoolean ? \"View all completed trainings\" : \"View overdue trainings\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 735,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 695,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"MHQV5rmcFGZBtb2m8YD7JtdoAFo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, isVesselView = false, hideCrewColumn = false, pageSize = 20 } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery)(\"(min-width: 720px)\");\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Training\",\n            cell: (param)=>{\n                let { row } = param;\n                const data = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: data,\n                    type: \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 763,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            cellAlignment: \"left\",\n            header: \"Vessel\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_vessel;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isVesselView == false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                        children: ((_due_vessel = due.vessel) === null || _due_vessel === void 0 ? void 0 : _due_vessel.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 776,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"crew\",\n            cellAlignment: \"right\",\n            header: \"Crew\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status;\n                const due = row.original;\n                const members = due.members || [];\n                return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: members.map((member)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: due.status.isOverdue ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 802,\n                            columnNumber: 33\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 799,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"!rounded-full size-10\", (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.class),\n                    children: members.length\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            cellAlignment: \"right\",\n            header: \"Status\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status, _due_status1, _due_status2;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? (_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                    children: ((_due_status2 = due.status) === null || _due_status2 === void 0 ? void 0 : _due_status2.label) || \"Unknown Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 855,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"crew\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n            columns: columns,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 872,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"  \",\n                        children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 882,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 881,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 880,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 879,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s1(OverdueTrainingList, \"mDuYwCIVI9QBRqOzz3Dco716Kgk=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery\n    ];\n});\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});