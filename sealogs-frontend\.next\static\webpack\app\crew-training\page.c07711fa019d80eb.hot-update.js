"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData)();\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D Raw training sessions data from API:\", response.readTrainingSessions);\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            console.log(\"\\uD83D\\uDD04 Transformed training sessions data:\", transformedData);\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            if (transformedData) {\n                setTrainingList(transformedData);\n                setVesselIdOptions(vesselIDs);\n                setTrainingTypeIdOptions(trainingTypeIDs);\n                setTrainerIdOptions(trainerIDs);\n                setCrewIdOptions(memberIDs);\n            }\n            setPageInfo(response.readTrainingSessions.pageInfo);\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadTrainingSessionDues(filter);\n        loadTrainingList(newPage, filter);\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: toggleOverdueWrapper\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: overdueBoolean,\n            setOverdue: toggleOverdueWrapper\n        };\n    }, [\n        handleFilterChange,\n        overdueBoolean,\n        toggleOverdueWrapper\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D Raw training session dues data from API:\", response.readTrainingSessionDues);\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                console.log(\"\\uD83D\\uDD04 Processed training session dues:\", mergedDues);\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match unified structure\n    const transformCompletedTrainings = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                trainer: training.trainer,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                trainingLocationType: training.trainingLocationType,\n                // Add priority for sorting (higher number = higher priority)\n                priority: 0\n            };\n        });\n    };\n    // Create unified and sorted training data\n    const createUnifiedTrainingData = ()=>{\n        // Transform completed trainings\n        const transformedCompleted = transformCompletedTrainings(trainingList || []);\n        // Add priority to overdue/upcoming trainings\n        const prioritizedDues = (trainingSessionDues || []).map((due)=>{\n            var _due_status, _due_status1;\n            return {\n                ...due,\n                priority: ((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? 3 : ((_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.dueWithinSevenDays) ? 2 : 1\n            };\n        });\n        // Combine all training data\n        const allTrainings = [\n            ...prioritizedDues,\n            ...transformedCompleted\n        ];\n        // Sort by priority (descending), then by due date (ascending for overdue/upcoming, descending for completed)\n        return allTrainings.sort((a, b)=>{\n            // First sort by priority (overdue > upcoming > other > completed)\n            if (a.priority !== b.priority) {\n                return b.priority - a.priority;\n            }\n            // Within same priority, sort by date\n            const dateA = new Date(a.dueDate || 0).getTime();\n            const dateB = new Date(b.dueDate || 0).getTime();\n            // For overdue and upcoming (priority > 0), sort by due date ascending (earliest first)\n            if (a.priority > 0) {\n                return dateA - dateB;\n            }\n            // For completed trainings (priority = 0), sort by date descending (most recent first)\n            return dateB - dateA;\n        });\n    };\n    // Combined loading state for unified view\n    const isUnifiedDataLoading = excludeFilters.includes(\"overdueToggle\") ? trainingListLoading || trainingSessionDuesLoading : false;\n    // Row status evaluation for FilteredTable styling\n    const getTrainingRowStatus = (training)=>{\n        if (!training.status) return \"normal\";\n        if (training.status.isOverdue) {\n            return \"overdue\";\n        }\n        if (training.status.dueWithinSevenDays) {\n            return \"upcoming\";\n        }\n        return \"normal\";\n    };\n    // Unified column definitions for the training table\n    const createUnifiedTrainingColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: \"Training\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__.MobileTrainingCard, {\n                        data: training,\n                        memberId: memberId,\n                        type: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.label) === \"Completed\" ? \"completed\" : \"overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_trainingType, _rowB_original1;\n                    // Sort by priority first, then by training type name\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    if (priorityA !== priorityB) {\n                        return priorityB - priorityA;\n                    }\n                    const nameA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const nameB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return nameA.localeCompare(nameB);\n                }\n            },\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_trainingType, _training_trainingTypes_nodes, _training_trainingTypes, _training_status, _training_status1;\n                    const training = row.original;\n                    const trainingType = ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : _training_trainingTypes_nodes.map((t)=>t.title).join(\", \")) || \"\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-sm font-medium\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: trainingType\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_trainingType, _rowA_original, _rowB_original_trainingType, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_13__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Crew\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    const members = training.members || [];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 justify-center\",\n                        children: [\n                            members.slice(0, 5).map((member)=>/*#__PURE__*/ {\n                                var _training_status, _training_status1;\n                                var _member_surname;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                                size: \"sm\",\n                                                variant: ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) ? \"destructive\" : ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) ? \"warning\" : \"secondary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                                    className: \"text-sm\",\n                                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(member.firstName, member.surname)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                            children: [\n                                                member.firstName,\n                                                \" \",\n                                                (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 33\n                                }, undefined);\n                            }),\n                            members.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground ml-1\",\n                                children: [\n                                    \"+\",\n                                    members.length - 5\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 542,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_members_, _rowA_original_members, _rowA_original, _rowA_original_members_1, _rowA_original_members1, _rowA_original1, _rowB_original_members_, _rowB_original_members, _rowB_original, _rowB_original_members_1, _rowB_original_members1, _rowB_original1;\n                    var _rowA_original_members__firstName, _rowA_original_members__surname;\n                    const valueA = \"\".concat((_rowA_original_members__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_ = _rowA_original_members[0]) === null || _rowA_original_members_ === void 0 ? void 0 : _rowA_original_members_.firstName) !== null && _rowA_original_members__firstName !== void 0 ? _rowA_original_members__firstName : \"\", \" \").concat((_rowA_original_members__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_1 = _rowA_original_members1[0]) === null || _rowA_original_members_1 === void 0 ? void 0 : _rowA_original_members_1.surname) !== null && _rowA_original_members__surname !== void 0 ? _rowA_original_members__surname : \"\") || \"\";\n                    var _rowB_original_members__firstName, _rowB_original_members__surname;\n                    const valueB = \"\".concat((_rowB_original_members__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_ = _rowB_original_members[0]) === null || _rowB_original_members_ === void 0 ? void 0 : _rowB_original_members_.firstName) !== null && _rowB_original_members__firstName !== void 0 ? _rowB_original_members__firstName : \"\", \" \").concat((_rowB_original_members__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_1 = _rowB_original_members1[0]) === null || _rowB_original_members_1 === void 0 ? void 0 : _rowB_original_members_1.surname) !== null && _rowB_original_members__surname !== void 0 ? _rowB_original_members__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1, _training_status2, _training_status3;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"px-2 py-1 rounded-md text-xs font-medium text-center\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"bg-destructive/10 text-destructive\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"bg-warning/10 text-warning\", ((_training_status2 = training.status) === null || _training_status2 === void 0 ? void 0 : _training_status2.label) === \"Completed\" && \"bg-muted text-muted-foreground\"),\n                        children: ((_training_status3 = training.status) === null || _training_status3 === void 0 ? void 0 : _training_status3.label) || \"Unknown\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const priorityA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.priority) || 0;\n                    const priorityB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.priority) || 0;\n                    return priorityB - priorityA;\n                }\n            },\n            {\n                accessorKey: \"dueDate\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 622,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_status, _training_status1;\n                    const training = row.original;\n                    const dateText = formatDate(training.dueDate);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-sm font-medium text-right\", ((_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.isOverdue) && \"text-cinnabar-500 hover:text-cinnabar-700\", ((_training_status1 = training.status) === null || _training_status1 === void 0 ? void 0 : _training_status1.dueWithinSevenDays) && \"text-fire-bush-600 hover:text-fire-bush-700\"),\n                        children: dateText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || 0).getTime();\n                    const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || 0).getTime();\n                    return dateB - dateA;\n                }\n            }\n        ]);\n    // Get unified training data based on filter settings\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            // Use the new unified and sorted data when overdueToggle is excluded\n            const unifiedData = createUnifiedTrainingData();\n            console.log(\"\\uD83D\\uDCCA Final unified training data for table:\", unifiedData);\n            return unifiedData;\n        }\n        // Return only overdue/upcoming trainings when toggle is available\n        const duesData = trainingSessionDues || [];\n        console.log(\"\\uD83D\\uDCCA Final training dues data for table:\", duesData);\n        return duesData;\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 679,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 681,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Get the unified training data\n    const unifiedTrainingData = getUnifiedTrainingData();\n    const isDataLoading = trainingListLoading || trainingSessionDuesLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: !overdueBoolean,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 693,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 691,\n                columnNumber: 13\n            }, undefined),\n            isDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 704,\n                        columnNumber: 21\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 703,\n                columnNumber: 17\n            }, undefined) : (unifiedTrainingData === null || unifiedTrainingData === void 0 ? void 0 : unifiedTrainingData.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.FilteredTable, {\n                columns: createUnifiedTrainingColumns(),\n                data: unifiedTrainingData,\n                rowStatus: getTrainingRowStatus,\n                pageSize: 20,\n                showToolbar: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 708,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 col-span-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 718,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 717,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 716,\n                columnNumber: 17\n            }, undefined),\n            !excludeFilters.includes(\"overdueToggle\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full\",\n                onClick: ()=>toggleOverdueWrapper((prev)=>!prev),\n                children: overdueBoolean ? \"View all completed trainings\" : \"View overdue trainings\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 730,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 690,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"rbQyWP9lfXqde+yv2viw1UUsYOA=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_14__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_12__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, isVesselView = false, hideCrewColumn = false, pageSize = 20 } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery)(\"(min-width: 720px)\");\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Training\",\n            cell: (param)=>{\n                let { row } = param;\n                const data = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_15__.MobileTrainingCard, {\n                    data: data,\n                    type: \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 758,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            cellAlignment: \"left\",\n            header: \"Vessel\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_vessel;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isVesselView == false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                        children: ((_due_vessel = due.vessel) === null || _due_vessel === void 0 ? void 0 : _due_vessel.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 771,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"crew\",\n            cellAlignment: \"right\",\n            header: \"Crew\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status;\n                const due = row.original;\n                const members = due.members || [];\n                return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: members.map((member)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                        size: \"sm\",\n                                        variant: due.status.isOverdue ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_10__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 797,\n                            columnNumber: 33\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 794,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"!rounded-full size-10\", (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.class),\n                    children: members.length\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 823,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            cellAlignment: \"right\",\n            header: \"Status\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status, _due_status1, _due_status2;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? (_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                    children: ((_due_status2 = due.status) === null || _due_status2 === void 0 ? void 0 : _due_status2.label) || \"Unknown Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 850,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"crew\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n            columns: columns,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 867,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"  \",\n                        children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 877,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 876,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 875,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 874,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s1(OverdueTrainingList, \"mDuYwCIVI9QBRqOzz3Dco716Kgk=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_16__.useMediaQuery\n    ];\n});\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});